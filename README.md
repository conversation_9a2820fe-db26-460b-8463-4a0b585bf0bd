# 📱 Screaming Focus Expo

A React Native Expo app that helps you focus by using your phone's orientation sensor. When you place your phone face down, <PERSON><PERSON><PERSON> goes to sleep. When you pick it up, the screaming begins! 😱

## 🎯 Features

- **Focus Timer**: Set sleep duration from 20-120 minutes
- **Orientation Detection**: Automatically detects when phone is face down
- **iOS App Blocking**: Uses Screen Time API to block distracting apps during focus sessions
- **Audio Engine**: Custom scream audio system
- **Keep Awake**: Prevents phone from sleeping during focus sessions
- **Development Build**: Full Expo development client support

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Yarn package manager
- iOS device with USB cable (iOS 15.0+ for Screen Time features)
- Xcode (for iOS builds)
- Apple Developer account (for device installation and Screen Time entitlements)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/DrSzav/screamingfocusexpo.git
   cd screamingfocusexpo
   ```

2. **Install dependencies**
   ```bash
   yarn install
   ```

3. **Install iOS dependencies**
   ```bash
   yarn pod-install
   ```

4. **Build and install to device**
   ```bash
   yarn ios-build-any
   ```

5. **Enable Screen Time Features (iOS)**
   - Grant Screen Time permissions when prompted
   - Set up shield extensions for custom blocking screens (see [SHIELD_EXTENSIONS_SETUP.md](SHIELD_EXTENSIONS_SETUP.md))
   - See [SCREEN_TIME_INTEGRATION.md](SCREEN_TIME_INTEGRATION.md) for detailed setup

## 📱 Development Commands

### iOS Building
```bash
yarn ios-build-any      # Build to any connected iPhone
yarn ios-build-device   # Build to specific device
yarn ios-clean          # Clean build cache
yarn ios-devices        # List connected devices
```

### Development
```bash
yarn start              # Start Metro bundler
yarn ios-device         # Run with Expo device selection
```

### CocoaPods Management
```bash
yarn pod-install        # Install pods
yarn pod-update         # Update pods
yarn pod-clean          # Clean and reinstall pods
```

## 🛠️ Tech Stack

- **React Native** 0.73.6
- **Expo SDK** 50
- **Native Base** UI components
- **Expo Sensors** for orientation detection
- **Expo AV** for audio playback
- **React Native Track Player** for audio management
- **Expo Keep Awake** to prevent sleep
- **iOS Screen Time API** for app blocking (iOS 15.0+)

## 📋 Project Structure

```
screamingfocusexpo/
├── App.js                 # Main app component
├── src/
│   ├── components/        # React components
│   ├── constants/         # App constants
│   ├── tests/             # Test files
│   ├── screamEngine.js    # Audio engine
│   └── ScreenTimeModule.js # Screen Time integration
├── assets/
│   ├── images/           # App images
│   ├── sounds/           # Audio files
│   └── fonts/            # Custom fonts
├── ios/                  # iOS native code
├── BUILD_COMMANDS.md     # Detailed build instructions
├── SCREEN_TIME_INTEGRATION.md # Screen Time setup guide
└── SHIELD_EXTENSIONS_SETUP.md # Custom blocking screens setup
```
