# iOS Build Commands

## Quick Commands

### 🚀 Build to Any Connected iPhone
```bash
yarn ios-build-any      # Builds to first available iPhone
```

### 📱 Build to Specific Device
```bash
yarn ios-build-device   # Builds to your specific iPhone (MoMuniMoHuni)
```

### 🔧 Development Commands
```bash
yarn start              # Start Metro bundler
yarn ios-device         # Run with Expo (device selection)
yarn ios-devices        # List all connected devices
yarn ios-clean          # Clean build cache
```

### 🛠️ CocoaPods Commands
```bash
yarn pod-install        # Install CocoaPods dependencies
yarn pod-update         # Update CocoaPods dependencies
yarn pod-clean          # Clean and reinstall pods
```

## Device Management

### Find Your Device ID
```bash
xcrun devicectl list devices
```

### Trust Your Device
1. Connect iPhone via USB
2. On iPhone: Settings > General > VPN & Device Management
3. Trust your computer's certificate

## Build Process

Each build command automatically:
1. 🔧 Installs/updates CocoaPods dependencies
2. 🔨 Builds the app with Xcode
3. 📱 Installs directly to your device

### Build Targets:
- `ios-build-any`: Builds to any connected iPhone
- `ios-build-device`: Builds to your specific device (ID: 00008110-001C390A3C68401E)
- `ios-build`: Generic iOS build (no device install)

## Troubleshooting

### If build fails:
```bash
# Clean and rebuild
yarn ios-clean
yarn pod-clean
yarn ios-build-any
```

### If device not detected:
- Ensure device is connected via USB
- Trust the computer on your device
- Check device appears in: `yarn ios-devices`

### If CocoaPods issues:
```bash
# Use Homebrew version
/opt/homebrew/bin/pod install
```

## Development Workflow

1. **First time setup:**
   ```bash
   yarn install
   yarn ios-build-any      # Installs pods and builds automatically
   ```

2. **Daily development:**
   ```bash
   yarn start              # Start Metro
   # Scan QR code on device or use reload button in app
   ```

3. **After dependency changes:**
   ```bash
   yarn ios-build-any      # Reinstalls pods and rebuilds
   ```

4. **Quick rebuild:**
   ```bash
   yarn ios-build-device   # Build to your specific device
   ```
