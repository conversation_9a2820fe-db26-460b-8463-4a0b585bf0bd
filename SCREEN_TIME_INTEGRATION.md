# iOS Screen Time API Integration

This document describes the iOS Screen Time API integration in the Screaming Focus app, which allows blocking other apps while the focus mode is active.

## Overview

The Screen Time integration uses Apple's Family Controls framework to restrict access to distracting apps during focus sessions. When a focus session is started (by pressing the "start" button), it immediately blocks access to social media, games, entertainment, and other potentially distracting app categories for the entire session duration.

## Features

- **Session-Based App Blocking**: Blocks distracting apps for the entire focus session duration
- **Custom Blocking Screens**: Shows branded blocking screens instead of just preventing access
- **Category-Based Blocking**: Blocks entire categories of apps (social media, games, etc.)
- **Pure Blocking Experience**: No interactive elements - users see message but cannot interact with blocking screens
- **Immediate Activation**: Starts blocking as soon as the focus session begins
- **Emergency Stop**: Allows users to end the session early if needed
- **Permission Management**: Handles Screen Time authorization requests
- **Cross-Platform Compatibility**: Gracefully degrades on non-iOS platforms

## Architecture

### Native iOS Components

1. **ScreenTimeManager.swift**: Main native module handling Screen Time API calls
2. **ScreenTimeManager.m**: Objective-C bridge for React Native integration
3. **ShieldConfigurationExtension.swift**: Defines custom blocking screen appearance (no interactions)
4. **Info.plist**: Contains required usage description for Family Controls
5. **Entitlements**: Includes Family Controls capability

### JavaScript Components

1. **ScreenTimeModule.js**: JavaScript interface for the native module
2. **ScreamAudio.js**: Updated to integrate Screen Time blocking
3. **App.js**: UI components for authorization and status display

## Setup Requirements

### iOS Configuration

1. **Minimum iOS Version**: iOS 15.0+ (Family Controls framework requirement)
2. **Xcode Version**: Xcode 13.0+ recommended
3. **Developer Account**: Required for Family Controls entitlement

### Entitlements

The app requires the Family Controls entitlement:
```xml
<key>com.apple.developer.family-controls</key>
<true/>
```

### Usage Description

Info.plist includes the required usage description:
```xml
<key>NSFamilyControlsUsageDescription</key>
<string>$(PRODUCT_NAME) uses Screen Time controls to help you focus by blocking distracting apps during focus sessions.</string>
```

## Usage

### Authorization

Before using Screen Time features, the app must request authorization:

```javascript
import ScreenTimeModule from './src/ScreenTimeModule';

// Request authorization
const result = await ScreenTimeModule.requestAuthorization();
if (result.status === 'authorized') {
  console.log('Screen Time authorized');
}
```

### Focus Mode Control

The Screen Time blocking is automatically integrated with the focus session lifecycle:

```javascript
// Start focus session (blocks apps for entire duration)
await startFocusSession();

// End focus session (unblocks apps)
await endFocusSession();
```

The blocking is active for the entire focus session duration, not just when the phone is picked up.

### Manual Control

You can also control focus mode manually:

```javascript
// Start blocking apps
await ScreenTimeModule.startFocusMode();

// Stop blocking apps
await ScreenTimeModule.stopFocusMode();

// Check if focus mode is active
const status = await ScreenTimeModule.isFocusModeActive();
```

## Blocked App Categories

The following app categories are blocked during focus mode:

- Social Networking
- Games
- Entertainment
- Productivity (optional)
- Creativity
- Education (optional)
- News
- Reference
- Finance
- Health & Fitness
- Travel
- Shopping
- Utilities (optional)

## Testing

### Manual Testing

1. Build and install the app on a physical iOS device
2. Grant Screen Time permissions when prompted
3. **Set up shield extensions** (see [SHIELD_EXTENSIONS_SETUP.md](SHIELD_EXTENSIONS_SETUP.md))
4. Start a focus session by setting a timer and pressing start
5. Try to open blocked apps - you should see custom blocking screens
6. Test blocking screen behavior:
   - Users see blocking screen with no interactive buttons
   - Must use home button/gesture to return to home screen
   - Focus session continues running
7. Place phone face down to stop the session (if not ended via shield)
8. Verify apps are unblocked

### Automated Testing

Run the test suite:

```javascript
import { ScreenTimeTest } from './src/tests/ScreenTimeTest';

// Run all tests
await ScreenTimeTest.runAllTests();

// Run individual tests
await ScreenTimeTest.testAuthorization();
await ScreenTimeTest.testFocusMode();
```

## Build Instructions

### Prerequisites

1. Install dependencies:
```bash
yarn install
yarn pod-install
```

2. Ensure you have a valid Apple Developer account
3. Configure signing in Xcode
4. **Set up shield extensions** (see [SHIELD_EXTENSIONS_SETUP.md](SHIELD_EXTENSIONS_SETUP.md))

### Building for Device

```bash
# Build and install on connected device
yarn ios-device

# Or build manually
yarn ios-build-device
```

**Note**: Shield extensions require manual setup in Xcode and cannot be automatically configured via React Native build scripts.

### Troubleshooting

#### Common Issues

1. **"Family Controls not available"**
   - Ensure iOS 15.0+ on device
   - Check entitlements are properly configured
   - Verify developer account has necessary permissions

2. **Authorization fails**
   - Check usage description in Info.plist
   - Ensure app is signed with valid certificate
   - Try deleting and reinstalling the app

3. **Apps not being blocked**
   - Verify authorization was granted
   - Check console logs for errors
   - Ensure focus mode is actually active

#### Debug Logging

Enable debug logging to troubleshoot issues:

```javascript
// In ScreenTimeModule.js, enable verbose logging
console.log('Screen Time operation:', operation, result);
```

## Limitations

1. **iOS Only**: Screen Time API is only available on iOS
2. **Physical Device Required**: Cannot test on iOS Simulator
3. **iOS 15.0+ Required**: Older iOS versions not supported
4. **Developer Account Required**: Family Controls requires paid developer account

## Future Enhancements

1. **Custom App Selection**: Allow users to select specific apps to block
2. **Time-Based Rules**: Different blocking rules for different times
3. **Whitelist Support**: Allow certain apps during focus mode
4. **Usage Analytics**: Track focus session effectiveness

## Security & Privacy

- The app only blocks apps during active focus sessions
- No app usage data is collected or transmitted
- All Screen Time controls are local to the device
- Users maintain full control over permissions

## Support

For issues related to Screen Time integration:

1. Check the troubleshooting section above
2. Review console logs for error messages
3. Ensure all setup requirements are met
4. Test on a physical iOS device with iOS 15.0+
