#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>

@interface RCTAdvancedAppStateModule : RCTEventEmitter <RCTBridgeModule>
@end

@implementation RCTAdvancedAppStateModule

RCT_EXPORT_MODULE();

- (NSArray<NSString *> *)supportedEvents {
  return @[@"appDidEnterBackground", @"appWillResignActive"];
}

- (instancetype)init {
  self = [super init];
  if (self) {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appDidEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appWillResignActive) name:UIApplicationWillResignActiveNotification object:nil];
  }
  return self;
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)appDidEnterBackground {
  [self sendEventWithName:@"appDidEnterBackground" body:nil];
}

- (void)appWillResignActive {
  [self sendEventWithName:@"appWillResignActive" body:nil];
}

@end

