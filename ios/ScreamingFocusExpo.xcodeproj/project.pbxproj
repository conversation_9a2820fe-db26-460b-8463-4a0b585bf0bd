// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		0B0F1B5C9E8D042698642875 /* 8.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = FA17AFC9681D9D26965B670F /* 8.mp3 */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		173DBD912A48471F00A72C1F /* AdvancedAppState.swift in Sources */ = {isa = PBXBuildFile; fileRef = 173DBD902A48471F00A72C1F /* AdvancedAppState.swift */; };
		174D08542A55810B00749BE0 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 174D08532A55810B00749BE0 /* Assets.xcassets */; };
		175E08A42B12345600C12345 /* ScreenTimeManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 175E08A32B12345600C12345 /* ScreenTimeManager.swift */; };
		175E08A62B12345700C12345 /* ScreenTimeManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 175E08A52B12345700C12345 /* ScreenTimeManager.m */; };
		316D323A4B50BAFCBD518938 /* 10.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 2F7B88A2328AA9DDA9FFE6A2 /* 10.mp3 */; };
		3A9F5DBA3132A146EC7DF122 /* libPods-ScreamingFocusExpo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FEC097CBDDB9866C2CA375E1 /* libPods-ScreamingFocusExpo.a */; };
		3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		47FDC56C433AA39D880A58A0 /* AudioManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1C620E42EF17FCF113CC1DEC /* AudioManager.m */; };
		55E483D501B6268DFDA10949 /* 6.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 39437BCDC8D311DC045362C6 /* 6.mp3 */; };
		5BDAD45DBAC2ECD0BDC326DB /* 5.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 3D342019767F3BC051E57000 /* 5.mp3 */; };
		615900F2F3904E5528591888 /* 9.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E1449E3F31203E233DF8E6B8 /* 9.mp3 */; };
		69FDD1D12DF5067B00806611 /* ManagedSettings.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 69FDD1D02DF5067A00806611 /* ManagedSettings.framework */; };
		69FDD1D32DF5067B00806611 /* ManagedSettingsUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 69FDD1D22DF5067B00806611 /* ManagedSettingsUI.framework */; };
		69FDD1DB2DF5067B00806611 /* ScreamingFocusShield.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 69FDD1CF2DF5067A00806611 /* ScreamingFocusShield.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		71AA1E3E1C1D7FDFF09099E7 /* 3.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 438916EA8E7FA4FF5C5CF9CF /* 3.mp3 */; };
		B18059E884C0ABDD17F3DC3D /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */; };
		B1D8D5C11467C4B9ABBB6448 /* 2.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = AC03E3E2779BCCD44B0482FF /* 2.mp3 */; };
		BB2F792D24A3F905000567C9 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		C9ABF1F8FA3155B96703BFBD /* 7.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 8E376B9EC7D5EF85572ED1FA /* 7.mp3 */; };
		E251F2FA5FF84B3F81D36023 /* noop-file.swift in Sources */ = {isa = PBXBuildFile; fileRef = C0C8637DA9FA4EC0B09A0A24 /* noop-file.swift */; };
		EA658456DAAF858E65AB1BB3 /* 4.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = E32026D41711DF8E3E997CBE /* 4.mp3 */; };
		F8B72CCD446980EFD4B1716C /* 1.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 9AB2D1235345209391105D84 /* 1.mp3 */; };
		FB96CB74A7490208EC98CEC8 /* AudioManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 478D6026D2D2A30B3FEF45C1 /* AudioManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		69FDD1D92DF5067B00806611 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 69FDD1CE2DF5067A00806611;
			remoteInfo = ScreamingFocusShield;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		69FDD1DC2DF5067B00806611 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				69FDD1DB2DF5067B00806611 /* ScreamingFocusShield.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		008F07F21AC5B25A0029DE68 /* main.jsbundle */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* ScreamingFocusExpo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ScreamingFocusExpo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = ScreamingFocusExpo/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = ScreamingFocusExpo/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = ScreamingFocusExpo/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = ScreamingFocusExpo/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = ScreamingFocusExpo/main.m; sourceTree = "<group>"; };
		173DBD902A48471F00A72C1F /* AdvancedAppState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedAppState.swift; sourceTree = "<group>"; };
		173DBD922A4847AB00A72C1F /* AdvancedAppState */ = {isa = PBXFileReference; lastKnownFileType = text; path = AdvancedAppState; sourceTree = "<group>"; };
		173DBD932A4848EA00A72C1F /* AdvancedAppState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AdvancedAppState.m; sourceTree = "<group>"; };
		174D08532A55810B00749BE0 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		175E08A32B12345600C12345 /* ScreenTimeManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScreenTimeManager.swift; sourceTree = "<group>"; };
		175E08A52B12345700C12345 /* ScreenTimeManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScreenTimeManager.m; sourceTree = "<group>"; };
		1C620E42EF17FCF113CC1DEC /* AudioManager.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; name = AudioManager.m; path = ScreamingFocusExpo/AudioManager.m; sourceTree = "<group>"; };
		2F7B88A2328AA9DDA9FFE6A2 /* 10.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 10.mp3; path = ScreamingFocusExpo/Audio/screamio/10.mp3; sourceTree = "<group>"; };
		39437BCDC8D311DC045362C6 /* 6.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 6.mp3; path = ScreamingFocusExpo/Audio/screamio/6.mp3; sourceTree = "<group>"; };
		3D342019767F3BC051E57000 /* 5.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 5.mp3; path = ScreamingFocusExpo/Audio/screamio/5.mp3; sourceTree = "<group>"; };
		438916EA8E7FA4FF5C5CF9CF /* 3.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 3.mp3; path = ScreamingFocusExpo/Audio/screamio/3.mp3; sourceTree = "<group>"; };
		478D6026D2D2A30B3FEF45C1 /* AudioManager.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AudioManager.swift; path = ScreamingFocusExpo/AudioManager.swift; sourceTree = "<group>"; };
		69FDD1CF2DF5067A00806611 /* ScreamingFocusShield.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ScreamingFocusShield.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		69FDD1D02DF5067A00806611 /* ManagedSettings.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ManagedSettings.framework; path = System/Library/Frameworks/ManagedSettings.framework; sourceTree = SDKROOT; };
		69FDD1D22DF5067B00806611 /* ManagedSettingsUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ManagedSettingsUI.framework; path = System/Library/Frameworks/ManagedSettingsUI.framework; sourceTree = SDKROOT; };
		8E376B9EC7D5EF85572ED1FA /* 7.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 7.mp3; path = ScreamingFocusExpo/Audio/screamio/7.mp3; sourceTree = "<group>"; };
		9AB2D1235345209391105D84 /* 1.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 1.mp3; path = ScreamingFocusExpo/Audio/screamio/1.mp3; sourceTree = "<group>"; };
		9E97892F8F8528F1598D7F76 /* Pods-ScreamingFocusExpo.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ScreamingFocusExpo.debug.xcconfig"; path = "Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo.debug.xcconfig"; sourceTree = "<group>"; };
		AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = SplashScreen.storyboard; path = ScreamingFocusExpo/SplashScreen.storyboard; sourceTree = "<group>"; };
		AC03E3E2779BCCD44B0482FF /* 2.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 2.mp3; path = ScreamingFocusExpo/Audio/screamio/2.mp3; sourceTree = "<group>"; };
		BB2F792C24A3F905000567C9 /* Expo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Expo.plist; sourceTree = "<group>"; };
		C0C8637DA9FA4EC0B09A0A24 /* noop-file.swift */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 4; includeInIndex = 0; lastKnownFileType = sourcecode.swift; name = "noop-file.swift"; path = "ScreamingFocusExpo/noop-file.swift"; sourceTree = "<group>"; };
		D9D503BFE46A97C5D45244B3 /* Pods-ScreamingFocusExpo.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ScreamingFocusExpo.release.xcconfig"; path = "Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo.release.xcconfig"; sourceTree = "<group>"; };
		E1449E3F31203E233DF8E6B8 /* 9.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 9.mp3; path = ScreamingFocusExpo/Audio/screamio/9.mp3; sourceTree = "<group>"; };
		E32026D41711DF8E3E997CBE /* 4.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 4.mp3; path = ScreamingFocusExpo/Audio/screamio/4.mp3; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		FA17AFC9681D9D26965B670F /* 8.mp3 */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = audio.mp3; name = 8.mp3; path = ScreamingFocusExpo/Audio/screamio/8.mp3; sourceTree = "<group>"; };
		FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-ScreamingFocusExpo/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		FEC097CBDDB9866C2CA375E1 /* libPods-ScreamingFocusExpo.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ScreamingFocusExpo.a"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		69FDD1DF2DF5067B00806611 /* Exceptions for "ScreamingFocusShield" folder in "ScreamingFocusShield" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 69FDD1CE2DF5067A00806611 /* ScreamingFocusShield */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		69FDD1D42DF5067B00806611 /* ScreamingFocusShield */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				69FDD1DF2DF5067B00806611 /* Exceptions for "ScreamingFocusShield" folder in "ScreamingFocusShield" target */,
			);
			path = ScreamingFocusShield;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				3A9F5DBA3132A146EC7DF122 /* libPods-ScreamingFocusExpo.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		69FDD1CC2DF5067A00806611 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				69FDD1D12DF5067B00806611 /* ManagedSettings.framework in Frameworks */,
				69FDD1D32DF5067B00806611 /* ManagedSettingsUI.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* ScreamingFocusExpo */ = {
			isa = PBXGroup;
			children = (
				174D08532A55810B00749BE0 /* Assets.xcassets */,
				173DBD902A48471F00A72C1F /* AdvancedAppState.swift */,
				173DBD922A4847AB00A72C1F /* AdvancedAppState */,
				175E08A32B12345600C12345 /* ScreenTimeManager.swift */,
				175E08A52B12345700C12345 /* ScreenTimeManager.m */,
				BB2F792B24A3F905000567C9 /* Supporting */,
				008F07F21AC5B25A0029DE68 /* main.jsbundle */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
				AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */,
				C0C8637DA9FA4EC0B09A0A24 /* noop-file.swift */,
				173DBD932A4848EA00A72C1F /* AdvancedAppState.m */,
				478D6026D2D2A30B3FEF45C1 /* AudioManager.swift */,
				1C620E42EF17FCF113CC1DEC /* AudioManager.m */,
				2D648BE98024AD385BBE8D1B /* Audio */,
			);
			name = ScreamingFocusExpo;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				FEC097CBDDB9866C2CA375E1 /* libPods-ScreamingFocusExpo.a */,
				69FDD1D02DF5067A00806611 /* ManagedSettings.framework */,
				69FDD1D22DF5067B00806611 /* ManagedSettingsUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		2D648BE98024AD385BBE8D1B /* Audio */ = {
			isa = PBXGroup;
			children = (
				3272E88EC09240E710F9EBE7 /* screamio */,
			);
			name = Audio;
			sourceTree = "<group>";
		};
		3272E88EC09240E710F9EBE7 /* screamio */ = {
			isa = PBXGroup;
			children = (
				9AB2D1235345209391105D84 /* 1.mp3 */,
				AC03E3E2779BCCD44B0482FF /* 2.mp3 */,
				438916EA8E7FA4FF5C5CF9CF /* 3.mp3 */,
				E32026D41711DF8E3E997CBE /* 4.mp3 */,
				3D342019767F3BC051E57000 /* 5.mp3 */,
				39437BCDC8D311DC045362C6 /* 6.mp3 */,
				8E376B9EC7D5EF85572ED1FA /* 7.mp3 */,
				FA17AFC9681D9D26965B670F /* 8.mp3 */,
				E1449E3F31203E233DF8E6B8 /* 9.mp3 */,
				2F7B88A2328AA9DDA9FFE6A2 /* 10.mp3 */,
			);
			name = screamio;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* ScreamingFocusExpo */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				69FDD1D42DF5067B00806611 /* ScreamingFocusShield */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				D65327D7A22EEC0BE12398D9 /* Pods */,
				D7E4C46ADA2E9064B798F356 /* ExpoModulesProviders */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* ScreamingFocusExpo.app */,
				69FDD1CF2DF5067A00806611 /* ScreamingFocusShield.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		92DBD88DE9BF7D494EA9DA96 /* ScreamingFocusExpo */ = {
			isa = PBXGroup;
			children = (
				FAC715A2D49A985799AEE119 /* ExpoModulesProvider.swift */,
			);
			name = ScreamingFocusExpo;
			sourceTree = "<group>";
		};
		BB2F792B24A3F905000567C9 /* Supporting */ = {
			isa = PBXGroup;
			children = (
				BB2F792C24A3F905000567C9 /* Expo.plist */,
			);
			name = Supporting;
			path = ScreamingFocusExpo/Supporting;
			sourceTree = "<group>";
		};
		D65327D7A22EEC0BE12398D9 /* Pods */ = {
			isa = PBXGroup;
			children = (
				9E97892F8F8528F1598D7F76 /* Pods-ScreamingFocusExpo.debug.xcconfig */,
				D9D503BFE46A97C5D45244B3 /* Pods-ScreamingFocusExpo.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		D7E4C46ADA2E9064B798F356 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				92DBD88DE9BF7D494EA9DA96 /* ScreamingFocusExpo */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* ScreamingFocusExpo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ScreamingFocusExpo" */;
			buildPhases = (
				5BD4BC92ABCAB43A25536484 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				EA8EFE91C14C4BDC3E0FDCAE /* [Expo] Configure project */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				B1C871E2E73F7B5E584DB9B4 /* [CP] Embed Pods Frameworks */,
				485251BC14741C4C8711A90F /* [CP] Copy Pods Resources */,
				69FDD1DC2DF5067B00806611 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				69FDD1DA2DF5067B00806611 /* PBXTargetDependency */,
			);
			name = ScreamingFocusExpo;
			productName = ScreamingFocusExpo;
			productReference = 13B07F961A680F5B00A75B9A /* ScreamingFocusExpo.app */;
			productType = "com.apple.product-type.application";
		};
		69FDD1CE2DF5067A00806611 /* ScreamingFocusShield */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 69FDD1E02DF5067B00806611 /* Build configuration list for PBXNativeTarget "ScreamingFocusShield" */;
			buildPhases = (
				69FDD1CB2DF5067A00806611 /* Sources */,
				69FDD1CC2DF5067A00806611 /* Frameworks */,
				69FDD1CD2DF5067A00806611 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				69FDD1D42DF5067B00806611 /* ScreamingFocusShield */,
			);
			name = ScreamingFocusShield;
			productName = ScreamingFocusShield;
			productReference = 69FDD1CF2DF5067A00806611 /* ScreamingFocusShield.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1250;
						ProvisioningStyle = Automatic;
					};
					69FDD1CE2DF5067A00806611 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ScreamingFocusExpo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			preferredProjectObjectVersion = 77;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* ScreamingFocusExpo */,
				69FDD1CE2DF5067A00806611 /* ScreamingFocusShield */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BB2F792D24A3F905000567C9 /* Expo.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				174D08542A55810B00749BE0 /* Assets.xcassets in Resources */,
				3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */,
				F8B72CCD446980EFD4B1716C /* 1.mp3 in Resources */,
				B1D8D5C11467C4B9ABBB6448 /* 2.mp3 in Resources */,
				71AA1E3E1C1D7FDFF09099E7 /* 3.mp3 in Resources */,
				EA658456DAAF858E65AB1BB3 /* 4.mp3 in Resources */,
				5BDAD45DBAC2ECD0BDC326DB /* 5.mp3 in Resources */,
				55E483D501B6268DFDA10949 /* 6.mp3 in Resources */,
				C9ABF1F8FA3155B96703BFBD /* 7.mp3 in Resources */,
				0B0F1B5C9E8D042698642875 /* 8.mp3 in Resources */,
				615900F2F3904E5528591888 /* 9.mp3 in Resources */,
				316D323A4B50BAFCBD518938 /* 10.mp3 in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		69FDD1CD2DF5067A00806611 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\n# The project root by default is one level up from the ios directory\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nif [[ \"$CONFIGURATION\" = *Debug* ]]; then\n  export SKIP_BUNDLING=1\nfi\nif [[ -z \"$ENTRY_FILE\" ]]; then\n  # Set the entry JS file using the bundler's entry resolution.\n  export ENTRY_FILE=\"$(\"$NODE_BINARY\" -e \"require('expo/scripts/resolveAppEntry')\" $PROJECT_ROOT ios relative | tail -n 1)\"\nfi\n\n`\"$NODE_BINARY\" --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/react-native-xcode.sh'\"`\n\n";
		};
		485251BC14741C4C8711A90F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5BD4BC92ABCAB43A25536484 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ScreamingFocusExpo-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B1C871E2E73F7B5E584DB9B4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ScreamingFocusExpo/Pods-ScreamingFocusExpo-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		EA8EFE91C14C4BDC3E0FDCAE /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-ScreamingFocusExpo/expo-configure-project.sh\"\n";
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\nexport RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > `$NODE_BINARY --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/.packager.env'\"`\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open `$NODE_BINARY --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/launchPackager.command'\"` || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				173DBD912A48471F00A72C1F /* AdvancedAppState.swift in Sources */,
				175E08A42B12345600C12345 /* ScreenTimeManager.swift in Sources */,
				175E08A62B12345700C12345 /* ScreenTimeManager.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				B18059E884C0ABDD17F3DC3D /* ExpoModulesProvider.swift in Sources */,
				E251F2FA5FF84B3F81D36023 /* noop-file.swift in Sources */,
				FB96CB74A7490208EC98CEC8 /* AudioManager.swift in Sources */,
				47FDC56C433AA39D880A58A0 /* AudioManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		69FDD1CB2DF5067A00806611 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		69FDD1DA2DF5067B00806611 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 69FDD1CE2DF5067A00806611 /* ScreamingFocusShield */;
			targetProxy = 69FDD1D92DF5067B00806611 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9E97892F8F8528F1598D7F76 /* Pods-ScreamingFocusExpo.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ScreamingFocusExpo/ScreamingFocusExpo.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UQV2CRRU4L;
				ENABLE_BITCODE = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				INFOPLIST_FILE = ScreamingFocusExpo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.drszav.screamingFocus;
				PRODUCT_NAME = ScreamingFocusExpo;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D9D503BFE46A97C5D45244B3 /* Pods-ScreamingFocusExpo.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ScreamingFocusExpo/ScreamingFocusExpo.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = UQV2CRRU4L;
				INFOPLIST_FILE = ScreamingFocusExpo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.drszav.screamingFocus;
				PRODUCT_NAME = ScreamingFocusExpo;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		69FDD1DD2DF5067B00806611 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = ScreamingFocusShield/ScreamingFocusShield.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = UQV2CRRU4L;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ScreamingFocusShield/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ScreamingFocusShield;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.drszav.screamingFocus.ScreamingFocusShield;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		69FDD1DE2DF5067B00806611 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_ENTITLEMENTS = ScreamingFocusShield/ScreamingFocusShield.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = UQV2CRRU4L;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ScreamingFocusShield/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ScreamingFocusShield;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MARKETING_VERSION = 1.0;
				MTL_FAST_MATH = YES;
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.drszav.screamingFocus.ScreamingFocusShield;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = "$(inherited)";
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "ScreamingFocusExpo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		69FDD1E02DF5067B00806611 /* Build configuration list for PBXNativeTarget "ScreamingFocusShield" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				69FDD1DD2DF5067B00806611 /* Debug */,
				69FDD1DE2DF5067B00806611 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "ScreamingFocusExpo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
