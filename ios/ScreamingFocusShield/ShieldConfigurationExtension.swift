//
//  ShieldConfigurationExtension.swift
//  ScreamingFocusShield
//
//  Created by DrSzav on 6/7/25.
//

import ManagedSettings
import ManagedSettingsUI
import UIKit

// Override the functions below to customize the shields used in various situations.
// The system provides a default appearance for any methods that your subclass doesn't override.
// Make sure that your class name matches the NSExtensionPrincipalClass in your Info.plist.
class ShieldConfigurationExtension: ShieldConfigurationDataSource {

    private func getSubtitleWithTimer(defaultMessage: String) -> String {
        let timeRemaining = ShieldMessages.getTimeRemaining()
        if timeRemaining.isEmpty {
            return defaultMessage
        }
        return "\(defaultMessage)\n\n\(timeRemaining)"
    }
    override func configuration(shielding application: Application) -> ShieldConfiguration {
        // Customize the shield as needed for applications.
        return ShieldConfiguration(
            backgroundBlurStyle: UIBlurEffect.Style.systemThickMaterial,
            backgroundColor: UIColor.systemBackground,
            icon: UIImage(named: "screemio", in: Bundle(for: Self.self), compatibleWith: nil) ?? UIImage(systemName: "moon.fill"),
            title: ShieldConfiguration.Label(
                text: ShieldMessages.getTitle(),
                color: UIColor.label
            ),
            subtitle: ShieldConfiguration.Label(
                text: getSubtitleWithTimer(defaultMessage: "This app is blocked during your focus session"),
                color: UIColor.secondaryLabel
            )
        )
    }
    
    override func configuration(shielding application: Application, in category: ActivityCategory) -> ShieldConfiguration {
        // Get category-specific message
        let message = ShieldMessages.getMessage(for: category)

        return ShieldConfiguration(
            backgroundBlurStyle: UIBlurEffect.Style.systemThickMaterial,
            backgroundColor: UIColor.systemBackground,
            icon: UIImage(named: "screemio", in: Bundle(for: Self.self), compatibleWith: nil) ?? UIImage(systemName: "moon.fill"),
            title: ShieldConfiguration.Label(
                text: ShieldMessages.getTitle(),
                color: UIColor.label
            ),
            subtitle: ShieldConfiguration.Label(
                text: getSubtitleWithTimer(defaultMessage: message),
                color: UIColor.secondaryLabel
            )
        )
    }
    
    override func configuration(shielding webDomain: WebDomain) -> ShieldConfiguration {
        // Customize the shield as needed for web domains.
        return ShieldConfiguration(
            backgroundBlurStyle: UIBlurEffect.Style.systemThickMaterial,
            backgroundColor: UIColor.systemBackground,
            icon: UIImage(named: "screemio", in: Bundle(for: Self.self), compatibleWith: nil) ?? UIImage(systemName: "moon.fill"),
            title: ShieldConfiguration.Label(
                text: ShieldMessages.getTitle(),
                color: UIColor.label
            ),
            subtitle: ShieldConfiguration.Label(
                text: getSubtitleWithTimer(defaultMessage: "Web browsing is blocked during focus 😞"),
                color: UIColor.secondaryLabel
            )
        )
    }
    
    override func configuration(shielding webDomain: WebDomain, in category: ActivityCategory) -> ShieldConfiguration {
        // Get category-specific message for web domains
        let message = ShieldMessages.getMessage(for: category)

        return ShieldConfiguration(
            backgroundBlurStyle: UIBlurEffect.Style.systemThickMaterial,
            backgroundColor: UIColor.systemBackground,
            icon: UIImage(named: "screemio", in: Bundle(for: Self.self), compatibleWith: nil) ?? UIImage(systemName: "moon.fill"),
            title: ShieldConfiguration.Label(
                text: ShieldMessages.getTitle(),
                color: UIColor.label
            ),
            subtitle: ShieldConfiguration.Label(
                text: getSubtitleWithTimer(defaultMessage: message),
                color: UIColor.secondaryLabel
            )
        )
    }


}

