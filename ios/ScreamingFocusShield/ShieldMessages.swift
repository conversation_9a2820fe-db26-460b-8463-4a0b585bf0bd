//
//  ShieldMessages.swift
//  ScreamingFocusShield
//
//  Created by DrSzav on 6/8/25.
//

import ManagedSettings
import Foundation

struct ShieldMessages {

    static func getTimeRemaining() -> String {
        let userDefaults = UserDefaults(suiteName: "group.com.screamingfocus.app")

        guard let sessionStartTime = userDefaults?.object(forKey: "focusSessionStartTime") as? Date,
              let sessionDuration = userDefaults?.object(forKey: "focusSessionDuration") as? TimeInterval else {
            return ""
        }

        let elapsed = Date().timeIntervalSince(sessionStartTime)
        let remaining = max(0, sessionDuration - elapsed)

        if remaining <= 0 {
            return "Session Complete"
        }

        // Round up to nearest minute
        let minutesLeft = Int(ceil(remaining / 60))

        if minutesLeft == 1 {
            return "(1 minute left)"
        } else {
            return "(\(minutesLeft) minutes left)"
        }
    }

    static func getTitle() -> String {
        let titles = [
            "agggghhhh",
            "come on",
            "stopppp",
            "dont do it",
            "noooooo",
            "seriously?",
            "not again",
            "whyyyy",
            "focus pls",
            "ughhhhh",
            "really tho?",
            "just dont",
            "nahhhh",
            "big sigh",
            "oh no"
        ]
        return titles.randomElement() ?? "stopppp"
    }

    static func getMessage(for category: ActivityCategory) -> String {
        let categoryName = getCategoryDisplayName(category).lowercased()
        
        var messages: [String] = []
        
        if categoryName.contains("social") {
            messages = [
                "Your followers will survive 😒",
                "Likes don't pay bills 💸",
                "Still hunting for validation? 🎭",
                "The algorithm doesn't love you back 🤖",
                "Scrolling through other people's lies 📱",
                "Your story expired hours ago 📸",
                "Influencing nobody, including yourself 🎪",
                "Double-tapping your way to nowhere 👆",
                "Comments section: where dreams die 💬",
                "Social media: antisocial behavior 🙃"
            ]
        } else if categoryName.contains("game") {
            messages = [
                "Your high score is disappointment 🏆",
                "Leveling up everything except your life 🎮",
                "Achievement unlocked: Procrastination Master 🏅",
                "The princess is in another castle 👸",
                "Game over, but life continues 💀",
                "Respawning in 3... 2... never ⏰",
                "Your avatar has better goals 🎯",
                "Grinding for virtual coins 💰",
                "Boss battle: Your own potential 👹",
                "Loading screen of life: 99% complete 📊"
            ]
        } else if categoryName.contains("entertainment") {
            messages = [
                "Binge-watching your potential away 📺",
                "The show must go on... without you 🎭",
                "Entertaining yourself to death 💀",
                "Plot twist: You're the side character 📖",
                "Intermission from your actual life 🎪",
                "Streaming your dreams down the drain 🌊",
                "The real entertainment is your excuses 🎨",
                "Popcorn ready, ambition not 🍿",
                "Starring in your own tragedy 🎬",
                "The credits roll on another wasted hour ⏳"
            ]
        } else if categoryName.contains("news") {
            messages = [
                "Breaking: You're still procrastinating 📰",
                "This just in: Nothing changed 📻",
                "Weather forecast: Cloudy with excuses ⛅",
                "Stock market: Your motivation is down 📉",
                "Local person discovers new way to avoid work 🗞️",
                "Traffic update: Stuck in your own way 🚗",
                "Sports: You're losing the game of life 🏈",
                "Headlines you'll never make 📺",
                "Breaking news: World continues without you 🌍",
                "In other news: Your goals are still waiting 📊"
            ]
        } else if categoryName.contains("shopping") {
            messages = [
                "Cart full of regret 🛒",
                "Retail therapy for a broken spirit 💳",
                "Shopping for happiness, finding debt 💸",
                "One-click closer to bankruptcy 📱",
                "Your wallet called, it's crying 😢",
                "Buying things you don't need 🛍️",
                "Amazon knows you better than yourself 📦",
                "Sale price: Your self-respect 🏷️",
                "Free shipping, expensive consequences 🚚",
                "Adding to cart, subtracting from life 🧮"
            ]
        } else if categoryName.contains("photo") || categoryName.contains("camera") {
            messages = [
                "Say cheese to your wasted time 📸",
                "Picture perfect procrastination 📷",
                "Capturing moments, losing hours ⏰",
                "Your selfie game is strong, life game weak 🤳",
                "Filtering reality since forever 📱",
                "Thousand words, zero progress 🖼️",
                "Smile! You're avoiding responsibility 😊",
                "Focus on the camera, blur your goals 📹",
                "Snapshot of disappointment 📸",
                "Developing bad habits 🎞️"
            ]
        } else if categoryName.contains("music") {
            messages = [
                "Your playlist is longer than your attention span 🎵",
                "Spotify knows your mood: Procrastination 🎧",
                "Dancing around your responsibilities 💃",
                "The beat drops, so does your productivity 🎶",
                "Shuffle mode: Your life priorities 🔀",
                "Volume up, motivation down 🔊",
                "Your anthem: 'I'll Do It Tomorrow' 🎤",
                "Headphones on, world off 🎧",
                "The only thing you're mixing is excuses 🎛️",
                "Sound of silence: Your achievements 🔇"
            ]
        } else if categoryName.contains("productivity") {
            messages = [
                "Productively avoiding productivity 📊",
                "Organizing your procrastination 📋",
                "To-do: Avoid doing 📝",
                "Efficiently wasting time ⚡",
                "Productivity app: 0% productive 📱",
                "Task completed: Avoiding tasks ✅",
                "Calendar full, progress empty 📅",
                "Meeting about not meeting deadlines 🤝",
                "Syncing everything except your life 🔄",
                "Optimizing your optimization tools 🛠️"
            ]
        } else if categoryName.contains("education") {
            messages = [
                "Learning how to avoid learning 📚",
                "Educated guess: You're procrastinating 🎓",
                "School of hard knocks: Enrollment pending 🏫",
                "Your degree in excuse-making 📜",
                "Pop quiz: When will you start? 📝",
                "Homework: Still not done 📖",
                "Class dismissed, motivation absent 🔔",
                "Study break that never ends ☕",
                "Knowledge is power, ignorance is bliss 💡",
                "Cramming for the test of life 📚"
            ]
        } else if categoryName.contains("health") || categoryName.contains("fitness") {
            messages = [
                "Healthy app, unhealthy habits 💊",
                "Fitness tracker: Steps to nowhere 👟",
                "Workout plan: Tomorrow, definitely 💪",
                "Calories burned: Zero, excuses made: Millions 🔥",
                "Your body is a temple, currently closed 🏛️",
                "Mental health day #365 🧠",
                "Meditation app: Mindfully procrastinating 🧘",
                "Wellness journey: Stuck at the starting line 🏃",
                "Healthy lifestyle: Loading... 📊",
                "Doctor's orders: Stop making excuses 👩‍⚕️"
            ]
        } else if categoryName.contains("finance") {
            messages = [
                "Investment tip: Invest in yourself 💰",
                "Portfolio performance: Underperforming 📈",
                "Budget: Time poorly allocated ⏰",
                "Compound interest: Your procrastination 📊",
                "Market analysis: You're in the red 📉",
                "Financial advisor: Get your life together 💼",
                "Savings account: Empty, excuse account: Full 🏦",
                "ROI: Return on Ignoring responsibilities 💸",
                "Credit score: Your motivation is declining 📋",
                "Bankruptcy: Of willpower 💳"
            ]
        } else if categoryName.contains("travel") {
            messages = [
                "Destination: Nowhere productive 🗺️",
                "Journey of a thousand excuses ✈️",
                "Passport to procrastination 📘",
                "Traveling in circles 🔄",
                "Vacation from responsibility 🏖️",
                "Road trip to disappointment 🚗",
                "Boarding pass to nowhere 🎫",
                "GPS: Recalculating your life 📍",
                "Miles traveled: Zero, time wasted: Infinite 🛣️",
                "Wanderlust for anything but work 🧳"
            ]
        } else if categoryName.contains("weather") {
            messages = [
                "Forecast: 100% chance of procrastination ⛅",
                "Storm warning: Your motivation 🌪️",
                "Sunny with a chance of excuses ☀️",
                "Weather update: Still avoiding work 📺",
                "Hurricane of distractions incoming 🌀",
                "Partly cloudy, fully unmotivated ⛈️",
                "Barometric pressure: Your willpower 📊",
                "Seasonal depression: Year-round 🍂",
                "Climate change: Your attitude needs it 🌡️",
                "Rainbow after the storm: Still waiting 🌈"
            ]
        } else if categoryName.contains("business") {
            messages = [
                "Business plan: Avoid business 📊",
                "Meeting about having meetings 🤝",
                "Synergy: You and procrastination 💼",
                "Quarterly review: Still disappointing 📈",
                "Market research: How to avoid work 📋",
                "Corporate ladder: You're in the basement 🪜",
                "Networking: With your couch 🛋️",
                "Profit margin: Negative motivation 💰",
                "Business model: Professional procrastinator 📱",
                "Exit strategy: From responsibility 🚪"
            ]
        } else if categoryName.contains("food") {
            messages = [
                "Recipe for disaster: Your priorities 🍽️",
                "Cooking up excuses 👨‍🍳",
                "Appetite for destruction 🍕",
                "Food for thought: You're hungry for distractions 🤔",
                "Meal prep: Preparing to procrastinate 📦",
                "Delivery time: Your motivation is delayed 🚚",
                "Ingredients: 100% pure avoidance 🥑",
                "Kitchen nightmare: Your life choices 🔥",
                "Comfort food: Uncomfortable truths 🍰",
                "Leftovers: Yesterday's unfinished tasks 📦"
            ]
        } else {
            messages = [
                "Congratulations: You found a new way to procrastinate 🎉",
                "Error 404: Motivation not found 🤖",
                "Loading... your excuses 📱",
                "This app is judging you 👁️",
                "Plot twist: You're the problem 🎭",
                "Achievement unlocked: Master Procrastinator 🏆",
                "Breaking news: You're still avoiding work 📺",
                "System update: Your priorities need debugging 🔧",
                "Connection lost: To your goals 📡",
                "Game over: Try again tomorrow 🎮"
            ]
        }
        
        return messages.randomElement() ?? "Error 404: Motivation not found 🤖"
    }
    
    private static func getCategoryDisplayName(_ category: ActivityCategory) -> String {
        if #available(iOS 16.0, *) {
            return category.localizedDisplayName ?? ""
        } else {
            return "App"
        }
    }
}
