#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(AudioManager, NSObject)

RCT_EXTERN_METHOD(setFocuser:(NSString *)focuser)

RCT_EXTERN_METHOD(startScreaming)

RCT_EXTERN_METHOD(stopScreaming)

RCT_EXTERN_METHOD(isCurrentlyScreaming:(RCTResponseSenderBlock)callback)

RCT_EXTERN_METHOD(setVolume:(float)volume)

RCT_EXTERN_METHOD(setSpeed:(float)speed)

+ (BOOL)requiresMainQueueSetup
{
  return NO;
}

@end
