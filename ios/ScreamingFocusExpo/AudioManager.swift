import Foundation
import AVFoundation
import React

@objc(AudioManager)
class AudioManager: NSObject {

    private var audioPlayer: AVAudioPlayer?
    private var isScreaming = false
    private var currentFocuser = "screamio"
    private var audioSession: AVAudioSession?
    private var currentVolume: Float = 1.0 // Store the volume to apply to new audio players
    
    override init() {
        super.init()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        audioSession = AVAudioSession.sharedInstance()

        do {
            // Configure audio session for background playback with proper options
            try audioSession?.setCategory(.playback, mode: .default, options: [.allowBluetooth, .allowBluetoothA2DP, .mixWithOthers])
            try audioSession?.setActive(true)

            print("Audio session configured for background playback")
        } catch {
            print("Failed to configure audio session: \(error)")
        }
    }
    
    @objc
    func setFocuser(_ focuser: String) {
        currentFocuser = focuser
        print("Focuser set to: \(focuser)")
    }
    
    @objc
    func startScreaming() {
        print("🎯 startScreaming() called")
        guard !isScreaming else {
            print("Already screaming")
            return
        }

        isScreaming = true
        print("Set isScreaming to true, calling playRandomScream()")
        playRandomScream()
        print("Started screaming with focuser: \(currentFocuser)")
    }
    
    @objc
    func stopScreaming() {
        isScreaming = false
        audioPlayer?.stop()
        audioPlayer = nil
        print("Stopped screaming")
    }
    
    @objc
    func isCurrentlyScreaming(_ callback: RCTResponseSenderBlock) {
        callback([isScreaming])
    }
    
    private func playRandomScream() {
        guard isScreaming else {
            print("Not screaming, skipping playback")
            return
        }

        let clipNumber = Int.random(in: 1...10)
        let audioFileName = "\(clipNumber)"

        print("Attempting to load audio file: \(audioFileName).mp3")
        print("Current focuser: \(currentFocuser)")

        // Try to find the file in the bundle root (files get flattened when added as resources)
        guard let audioPath = Bundle.main.path(forResource: audioFileName, ofType: "mp3") else {
            print("❌ Could not find audio file: \(audioFileName).mp3")

            // Debug: List all available MP3 files in bundle
            if let bundlePath = Bundle.main.resourcePath {
                print("Bundle resource path: \(bundlePath)")

                do {
                    let allFiles = try FileManager.default.contentsOfDirectory(atPath: bundlePath)
                    let mp3Files = allFiles.filter { $0.hasSuffix(".mp3") }
                    print("Available MP3 files in bundle: \(mp3Files)")
                } catch {
                    print("Error listing bundle contents: \(error)")
                }
            }
            return
        }

        print("✅ Found audio file at: \(audioPath)")
        let audioURL = URL(fileURLWithPath: audioPath)

        do {
            audioPlayer = try AVAudioPlayer(contentsOf: audioURL)
            audioPlayer?.delegate = self

            // Apply the stored volume to the new audio player
            audioPlayer?.volume = currentVolume
            print("🎵 Audio player created and volume set to: \(currentVolume)")

            audioPlayer?.prepareToPlay()

            print("Audio player prepared, starting playback...")
            let success = audioPlayer?.play() ?? false
            print("Playback started: \(success)")
            print("Current audio player volume: \(audioPlayer?.volume ?? 0)")

            if success {
                print("🔊 Playing audio: \(audioFileName).mp3 at volume \(audioPlayer?.volume ?? 0)")
            } else {
                print("❌ Failed to start audio playback")
            }
        } catch {
            print("❌ Error creating audio player: \(error)")
        }
    }
    
    @objc
    func setVolume(_ volume: Float) {
        // Use the actual volume passed from JavaScript (accelerometer-based)
        let clampedVolume = max(0.0, min(1.0, volume)) // Ensure volume is between 0 and 1
        currentVolume = clampedVolume // Store for new audio players
        audioPlayer?.volume = clampedVolume // Apply to current player if it exists
        print("🔊 Volume set to: \(clampedVolume) (requested: \(volume)), applied to current player: \(audioPlayer != nil)")
    }
    
    @objc
    func setSpeed(_ speed: Float) {
        audioPlayer?.rate = speed
    }
}

// MARK: - AVAudioPlayerDelegate
extension AudioManager: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        if flag && isScreaming {
            // Play next random scream
            playRandomScream()
        }
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        print("Audio player decode error: \(error?.localizedDescription ?? "Unknown error")")
        if isScreaming {
            // Try to play next scream on error
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.playRandomScream()
            }
        }
    }
}
