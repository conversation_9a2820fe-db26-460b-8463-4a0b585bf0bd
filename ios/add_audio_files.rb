#!/usr/bin/env ruby

require 'xcodeproj'

# Open the Xcode project
project_path = 'ScreamingFocusExpo.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Get the main target
target = project.targets.find { |t| t.name == 'ScreamingFocusExpo' }

# Get the main group
main_group = project.main_group.find_subpath('ScreamingFocusExpo')

# Add AudioManager Swift and Objective-C files (only if they don't exist)
unless main_group.find_file_by_path('ScreamingFocusExpo/AudioManager.swift')
  audio_manager_swift = main_group.new_reference('ScreamingFocusExpo/AudioManager.swift')
  target.source_build_phase.add_file_reference(audio_manager_swift)
end

unless main_group.find_file_by_path('ScreamingFocusExpo/AudioManager.m')
  audio_manager_objc = main_group.new_reference('ScreamingFocusExpo/AudioManager.m')
  target.source_build_phase.add_file_reference(audio_manager_objc)
end

# Create Audio group if it doesn't exist
audio_group = main_group.find_subpath('Audio') || main_group.new_group('Audio')

# Add screamio audio files only
screamio_group = audio_group.find_subpath('screamio') || audio_group.new_group('screamio')
(1..10).each do |i|
  file_path = "ScreamingFocusExpo/Audio/screamio/#{i}.mp3"
  unless screamio_group.find_file_by_path(file_path)
    file_ref = screamio_group.new_reference(file_path)
    target.resources_build_phase.add_file_reference(file_ref)
  end
end

# Save the project
project.save

puts "AudioManager files and screamio audio resources added to Xcode project successfully!"
