//
//  ScreenTimeManager.swift
//  ScreamingFocusExpo
//
//  Created by Augment Agent
//

import Foundation
import FamilyControls
import ManagedSettings
import DeviceActivity
import React
import UIKit

@objc(ScreenTimeManager)
class ScreenTimeManager: RCTEventEmitter {

  private let store = ManagedSettingsStore()
  private let center = AuthorizationCenter.shared

  // TikTok monitoring
  private var tiktokMonitoringActive = false
  private var tiktokUsageStartTime: Date?
  private var tiktokUsageTimer: Timer?
  private let tiktokUsageThreshold: TimeInterval = 60 // 1 minute
  
  override init() {
    super.init()
  }
  
  @objc
  static override func requiresMainQueueSetup() -> Bool {
    return true
  }
  
  override func supportedEvents() -> [String]! {
    return ["onAuthorizationChanged", "onScreenTimeError", "onTikTokUsageExceeded"]
  }
  
  // MARK: - Authorization
  
  @objc
  func requestAuthorization(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    if #available(iOS 16.0, *) {
      Task {
        do {
          try await center.requestAuthorization(for: .individual)
          DispatchQueue.main.async {
            resolve(["status": "authorized"])
          }
        } catch {
          DispatchQueue.main.async {
            reject("AUTHORIZATION_ERROR", "Failed to get Screen Time authorization: \(error.localizedDescription)", error)
          }
        }
      }
    } else {
      // For iOS 15.0, use the completion handler version
      center.requestAuthorization { result in
        DispatchQueue.main.async {
          switch result {
          case .success:
            resolve(["status": "authorized"])
          case .failure(let error):
            reject("AUTHORIZATION_ERROR", "Failed to get Screen Time authorization: \(error.localizedDescription)", error)
          }
        }
      }
    }
  }
  
  @objc
  func getAuthorizationStatus(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    let status = center.authorizationStatus
    let statusString: String
    
    switch status {
    case .notDetermined:
      statusString = "notDetermined"
    case .denied:
      statusString = "denied"
    case .approved:
      statusString = "approved"
    @unknown default:
      statusString = "unknown"
    }
    
    resolve(["status": statusString])
  }
  
  // MARK: - App Blocking
  
  @objc
  func startFocusMode(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    guard center.authorizationStatus == .approved else {
      reject("NOT_AUTHORIZED", "Screen Time authorization not granted", nil)
      return
    }
    
    // Block ALL applications except essential system apps
    // Use the all policy to block all apps in all categories
    store.shield.applicationCategories = ShieldSettings.ActivityCategoryPolicy.all()

    // Note: Web domain blocking requires specific domain tokens
    // For comprehensive blocking, we focus on app categories
    // Web domains can be added later with specific WebDomainTokens

    resolve(["success": true, "message": "Focus mode activated - all apps blocked"])
  }
  
  @objc
  func stopFocusMode(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    // Remove all restrictions
    if #available(iOS 16.0, *) {
      store.clearAllSettings()
    } else {
      // For iOS 15.0, manually clear each setting
      store.shield.applications = Set()
      store.shield.applicationCategories = ShieldSettings.ActivityCategoryPolicy.none
      store.shield.webDomains = Set()
    }

    resolve(["success": true, "message": "Focus mode deactivated"])
  }
  
  @objc
  func isFocusModeActive(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    // Check if any restrictions are currently active
    let hasActiveRestrictions = !(store.shield.applications?.isEmpty ?? true) ||
                               !(store.shield.webDomains?.isEmpty ?? true) ||
                               store.shield.applicationCategories != ShieldSettings.ActivityCategoryPolicy.none

    resolve(["isActive": hasActiveRestrictions])
  }
  
  // MARK: - Custom App Selection
  
  @objc
  func selectAppsToBlock(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    guard center.authorizationStatus == .approved else {
      reject("NOT_AUTHORIZED", "Screen Time authorization not granted", nil)
      return
    }

    // This would typically present the FamilyActivityPicker
    // For now, we'll use predefined categories
    resolve(["message": "App selection completed"])
  }

  // MARK: - Timer Management

  @objc
  func setFocusSessionTimer(_ startTime: Double, duration: Double, resolver resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    let userDefaults = UserDefaults(suiteName: "group.com.screamingfocus.app")
    let sessionStartTime = Date(timeIntervalSince1970: startTime / 1000) // Convert from JS timestamp

    userDefaults?.set(sessionStartTime, forKey: "focusSessionStartTime")
    userDefaults?.set(duration, forKey: "focusSessionDuration")
    userDefaults?.synchronize()

    resolve(["success": true, "message": "Timer set"])
  }

  @objc
  func clearFocusSessionTimer(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    let userDefaults = UserDefaults(suiteName: "group.com.screamingfocus.app")
    userDefaults?.removeObject(forKey: "focusSessionStartTime")
    userDefaults?.removeObject(forKey: "focusSessionDuration")
    userDefaults?.synchronize()

    resolve(["success": true, "message": "Timer cleared"])
  }

  // MARK: - TikTok Usage Monitoring

  @objc
  func startTikTokMonitoring(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    guard !tiktokMonitoringActive else {
      resolve(["success": true, "message": "TikTok monitoring already active"])
      return
    }

    // Start simple monitoring approach
    tiktokMonitoringActive = true
    startAppStateMonitoring()

    resolve(["success": true, "message": "TikTok monitoring started"])
    print("TikTok monitoring started successfully")
  }

  @objc
  func stopTikTokMonitoring(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    guard tiktokMonitoringActive else {
      resolve(["success": true, "message": "TikTok monitoring not active"])
      return
    }

    stopAppStateMonitoring()
    tiktokMonitoringActive = false
    resolve(["success": true, "message": "TikTok monitoring stopped"])
    print("TikTok monitoring stopped")
  }

  @objc
  func isTikTokMonitoringActive(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    resolve(["isActive": tiktokMonitoringActive])
  }

  @objc
  func testTikTokUsageExceeded(_ resolve: @escaping RCTPromiseResolveBlock, rejecter reject: @escaping RCTPromiseRejectBlock) {
    print("Manually triggering TikTok usage exceeded for testing")
    handleTikTokUsageExceeded()
    resolve(["success": true, "message": "TikTok usage exceeded event triggered"])
  }

  // MARK: - Simplified App State Monitoring

  private func startAppStateMonitoring() {
    // Listen for app state changes to detect when user might be using TikTok
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(appDidBecomeActive),
      name: UIApplication.didBecomeActiveNotification,
      object: nil
    )

    NotificationCenter.default.addObserver(
      self,
      selector: #selector(appDidEnterBackground),
      name: UIApplication.didEnterBackgroundNotification,
      object: nil
    )

    print("Started app state monitoring for TikTok usage")
  }

  private func stopAppStateMonitoring() {
    NotificationCenter.default.removeObserver(self, name: UIApplication.didBecomeActiveNotification, object: nil)
    NotificationCenter.default.removeObserver(self, name: UIApplication.didEnterBackgroundNotification, object: nil)

    // Stop any active timer
    tiktokUsageTimer?.invalidate()
    tiktokUsageTimer = nil
    tiktokUsageStartTime = nil

    print("Stopped app state monitoring for TikTok usage")
  }

  @objc private func appDidBecomeActive() {
    // When our app becomes active, check if user was potentially using TikTok
    if tiktokMonitoringActive {
      checkForTikTokUsage()
    }
  }

  @objc private func appDidEnterBackground() {
    // When our app goes to background, start monitoring for potential TikTok usage
    if tiktokMonitoringActive {
      startTikTokUsageTimer()
    }
  }

  private func startTikTokUsageTimer() {
    // Start a timer that will trigger after 1 minute
    tiktokUsageStartTime = Date()
    tiktokUsageTimer = Timer.scheduledTimer(withTimeInterval: tiktokUsageThreshold, repeats: false) { [weak self] _ in
      self?.handleTikTokUsageExceeded()
    }
    print("Started TikTok usage timer")
  }

  private func checkForTikTokUsage() {
    // If we have an active timer, it means user was potentially using another app
    if let timer = tiktokUsageTimer, timer.isValid {
      timer.invalidate()
      tiktokUsageTimer = nil

      // Check if enough time passed to consider it TikTok usage
      if let startTime = tiktokUsageStartTime {
        let timeSpent = Date().timeIntervalSince(startTime)
        if timeSpent >= tiktokUsageThreshold {
          handleTikTokUsageExceeded()
        }
      }
    }
  }

  private func handleTikTokUsageExceeded() {
    print("TikTok usage threshold exceeded!")
    sendEvent(withName: "onTikTokUsageExceeded", body: ["timestamp": Date().timeIntervalSince1970])

    // Store in UserDefaults for React Native to pick up
    let userDefaults = UserDefaults(suiteName: "group.com.screamingfocus.app")
    userDefaults?.set(Date().timeIntervalSince1970, forKey: "tiktokUsageExceededTimestamp")
    userDefaults?.set(true, forKey: "tiktokUsageExceeded")
    userDefaults?.synchronize()
  }

}
