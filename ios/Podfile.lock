PODS:
  - boost (1.83.0)
  - DoubleConversion (1.1.6)
  - EXApplication (5.8.4):
    - ExpoModulesCore
  - EXAV (13.10.6):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXConstants (15.4.6):
    - ExpoModulesCore
  - EXFont (11.10.3):
    - ExpoModulesCore
  - EXJSONUtils (0.12.3)
  - EXManifests (0.13.2):
    - ExpoModulesCore
  - EXNotifications (0.27.8):
    - ExpoModulesCore
  - Expo (50.0.21):
    - ExpoModulesCore
  - expo-dev-client (3.3.12):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (3.6.10):
    - EXManifests
    - expo-dev-launcher/Main (= 3.6.10)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Main (3.6.10):
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Unsafe (3.6.10):
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-menu (4.5.8):
    - expo-dev-menu/Main (= 4.5.8)
    - expo-dev-menu/ReactNativeCompatibles (= 4.5.8)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - expo-dev-menu-interface (1.7.2)
  - expo-dev-menu/Main (4.5.8):
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - expo-dev-menu/ReactNativeCompatibles (4.5.8):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - expo-dev-menu/SafeAreaView (4.5.8):
    - ExpoModulesCore
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - expo-dev-menu/Vendored (4.5.8):
    - expo-dev-menu/SafeAreaView
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - ExpoFileSystem (16.0.9):
    - ExpoModulesCore
  - ExpoImage (1.10.6):
    - ExpoModulesCore
    - SDWebImage (~> 5.17.0)
    - SDWebImageAVIFCoder (~> 0.10.1)
    - SDWebImageSVGCoder (~> 1.7.0)
    - SDWebImageWebPCoder (~> 0.13.0)
  - ExpoKeepAwake (12.8.2):
    - ExpoModulesCore
  - ExpoModulesCore (1.11.14):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - ReactCommon/turbomodule/core
  - ExpoScreenOrientation (6.4.1):
    - ExpoModulesCore
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - ExpoSensors (12.9.1):
    - ExpoModulesCore
  - EXSplashScreen (0.26.5):
    - ExpoModulesCore
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core
  - EXUpdatesInterface (0.15.3)
  - FBLazyVector (0.73.6)
  - FBReactNativeSpec (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.6)
    - RCTTypeSafety (= 0.73.6)
    - React-Core (= 0.73.6)
    - React-jsi (= 0.73.6)
    - ReactCommon/turbomodule/core (= 0.73.6)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.73.6):
    - hermes-engine/Pre-built (= 0.73.6)
  - hermes-engine/Pre-built (0.73.6)
  - libaom (3.0.0):
    - libvmaf (>= 2.2.0)
  - libavif (0.11.1):
    - libavif/libaom (= 0.11.1)
  - libavif/core (0.11.1)
  - libavif/libaom (0.11.1):
    - libaom (>= 2.0.0)
    - libavif/core
  - libevent (2.1.12)
  - libvmaf (2.3.1)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mute (0.6.1)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.6)
  - RCTTypeSafety (0.73.6):
    - FBLazyVector (= 0.73.6)
    - RCTRequired (= 0.73.6)
    - React-Core (= 0.73.6)
  - React (0.73.6):
    - React-Core (= 0.73.6)
    - React-Core/DevSupport (= 0.73.6)
    - React-Core/RCTWebSocket (= 0.73.6)
    - React-RCTActionSheet (= 0.73.6)
    - React-RCTAnimation (= 0.73.6)
    - React-RCTBlob (= 0.73.6)
    - React-RCTImage (= 0.73.6)
    - React-RCTLinking (= 0.73.6)
    - React-RCTNetwork (= 0.73.6)
    - React-RCTSettings (= 0.73.6)
    - React-RCTText (= 0.73.6)
    - React-RCTVibration (= 0.73.6)
  - React-callinvoker (0.73.6)
  - React-Codegen (0.73.6):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-Core/RCTWebSocket (= 0.73.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.6)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.6)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.6)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.6):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-debug (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-jsinspector (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
    - React-runtimeexecutor (= 0.73.6)
  - React-debug (0.73.6)
  - React-Fabric (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.6)
    - React-Fabric/attributedstring (= 0.73.6)
    - React-Fabric/componentregistry (= 0.73.6)
    - React-Fabric/componentregistrynative (= 0.73.6)
    - React-Fabric/components (= 0.73.6)
    - React-Fabric/core (= 0.73.6)
    - React-Fabric/imagemanager (= 0.73.6)
    - React-Fabric/leakchecker (= 0.73.6)
    - React-Fabric/mounting (= 0.73.6)
    - React-Fabric/scheduler (= 0.73.6)
    - React-Fabric/telemetry (= 0.73.6)
    - React-Fabric/templateprocessor (= 0.73.6)
    - React-Fabric/textlayoutmanager (= 0.73.6)
    - React-Fabric/uimanager (= 0.73.6)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.6)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.6)
    - React-Fabric/components/modal (= 0.73.6)
    - React-Fabric/components/rncore (= 0.73.6)
    - React-Fabric/components/root (= 0.73.6)
    - React-Fabric/components/safeareaview (= 0.73.6)
    - React-Fabric/components/scrollview (= 0.73.6)
    - React-Fabric/components/text (= 0.73.6)
    - React-Fabric/components/textinput (= 0.73.6)
    - React-Fabric/components/unimplementedview (= 0.73.6)
    - React-Fabric/components/view (= 0.73.6)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.6)
    - RCTTypeSafety (= 0.73.6)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.6)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.6):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.6)
    - React-utils
  - React-hermes (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.6)
    - React-jsi
    - React-jsiexecutor (= 0.73.6)
    - React-jsinspector (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - React-ImageManager (0.73.6):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.6):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.6):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - React-jsinspector (0.73.6)
  - React-logger (0.73.6):
    - glog
  - React-Mapbuffer (0.73.6):
    - glog
    - React-debug
  - react-native-keep-awake (4.0.0):
    - React
  - react-native-safe-area-context (4.8.2):
    - React-Core
  - react-native-screen-brightness (2.0.0-alpha):
    - React
  - react-native-switch-audio-output (1.1.2):
    - React
  - react-native-track-player (3.2.0):
    - React-Core
    - SwiftAudioEx (= 0.15.3)
  - react-native-volume-manager (1.8.1):
    - Mute
    - React-Core
  - React-nativeconfig (0.73.6)
  - React-NativeModulesApple (0.73.6):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.6)
  - React-RCTActionSheet (0.73.6):
    - React-Core/RCTActionSheetHeaders (= 0.73.6)
  - React-RCTAnimation (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.6):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.6):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.6):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.6)
  - React-RCTNetwork (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.6):
    - React-Core/RCTTextHeaders (= 0.73.6)
    - Yoga
  - React-RCTVibration (0.73.6):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.6)
  - React-runtimeexecutor (0.73.6):
    - React-jsi (= 0.73.6)
  - React-runtimescheduler (0.73.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.6):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.6):
    - React-logger (= 0.73.6)
    - ReactCommon/turbomodule (= 0.73.6)
  - ReactCommon/turbomodule (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
    - ReactCommon/turbomodule/bridging (= 0.73.6)
    - ReactCommon/turbomodule/core (= 0.73.6)
  - ReactCommon/turbomodule/bridging (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - ReactCommon/turbomodule/core (0.73.6):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.6)
    - React-cxxreact (= 0.73.6)
    - React-jsi (= 0.73.6)
    - React-logger (= 0.73.6)
    - React-perflogger (= 0.73.6)
  - RNCAsyncStorage (2.2.0):
    - React-Core
  - RNSVG (14.1.0):
    - React-Core
  - SDWebImage (5.17.0):
    - SDWebImage/Core (= 5.17.0)
  - SDWebImage/Core (5.17.0)
  - SDWebImageAVIFCoder (0.10.1):
    - libavif (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageSVGCoder (1.7.0):
    - SDWebImage/Core (~> 5.6)
  - SDWebImageWebPCoder (0.13.0):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - SocketRocket (0.6.1)
  - SwiftAudioEx (0.15.3)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXApplication (from `../node_modules/expo-application/ios`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFont (from `../node_modules/expo-font/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - EXNotifications (from `../node_modules/expo-notifications/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoImage (from `../node_modules/expo-image/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoScreenOrientation (from `../node_modules/expo-screen-orientation/ios`)
  - ExpoSensors (from `../node_modules/expo-sensors/ios`)
  - EXSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - react-native-keep-awake (from `../node_modules/react-native-keep-awake`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-screen-brightness (from `../node_modules/react-native-screen-brightness`)
  - react-native-switch-audio-output (from `../node_modules/react-native-switch-audio-output`)
  - react-native-track-player (from `../node_modules/react-native-track-player`)
  - react-native-volume-manager (from `../node_modules/react-native-volume-manager`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - fmt
    - libaom
    - libavif
    - libevent
    - libvmaf
    - libwebp
    - Mute
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageSVGCoder
    - SDWebImageWebPCoder
    - SocketRocket
    - SwiftAudioEx

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXApplication:
    :path: "../node_modules/expo-application/ios"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFont:
    :path: "../node_modules/expo-font/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  EXNotifications:
    :path: "../node_modules/expo-notifications/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoImage:
    :path: "../node_modules/expo-image/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoScreenOrientation:
    :path: "../node_modules/expo-screen-orientation/ios"
  ExpoSensors:
    :path: "../node_modules/expo-sensors/ios"
  EXSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-02-20-RNv0.73.5-18f99ace4213052c5e7cdbcd39ee9766cd5df7e4
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  react-native-keep-awake:
    :path: "../node_modules/react-native-keep-awake"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-screen-brightness:
    :path: "../node_modules/react-native-screen-brightness"
  react-native-switch-audio-output:
    :path: "../node_modules/react-native-switch-audio-output"
  react-native-track-player:
    :path: "../node_modules/react-native-track-player"
  react-native-volume-manager:
    :path: "../node_modules/react-native-volume-manager"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: d3f49c53809116a5d38da093a8aa78bf551aed09
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  EXApplication: d62cd534090692cbf538ea15f69f829b517a40b1
  EXAV: e160fec51bf6bc41c3b6f7e4c72ea9fd36860cd6
  EXConstants: c82ef9280044accc91fb6082047afdc9a408f61a
  EXFont: 64e653a110eee050ad80dfcd676c4bada0a1ff92
  EXJSONUtils: 5c42959e87be238b045ef37cc5268b16a6c0ad4a
  EXManifests: 429136cffa3ae82d1ba3b60b7243fb186615562e
  EXNotifications: 9fd42ca3c5998ae93a2ac869ed9a47467c433219
  Expo: 1b33cb8ab60cff9abf805ed6020af3d1846e457c
  expo-dev-client: 775a683302570193a7ba71032d0b1b82f6ad1454
  expo-dev-launcher: 4b9190f0879290b57ed89cc5c24592d4ce981f85
  expo-dev-menu: efe7323e7f918e437a02f20063eb2b55c13dfe99
  expo-dev-menu-interface: 44e69ddff62bbc6c5418c200e657635720b5a480
  ExpoFileSystem: df58e1eb2a4d6f1006a1ca70bddfbbf63e52fa4f
  ExpoImage: a70db90f39a7af98930cef91c84e877b1131f3dd
  ExpoKeepAwake: 3b8cf8533b9212500565a1e41fb080fc5af29918
  ExpoModulesCore: 43ebd65adf2c0e6ffb0de24a41f8f7de12829331
  ExpoScreenOrientation: 6cb08993736d1bafeaad09b5126928a98ea65261
  ExpoSensors: 7101785175140e8be722797b501987a44893bff6
  EXSplashScreen: dd2dd171af558302d925bb90e3bd2e139ffa0d92
  EXUpdatesInterface: 3e444e2093e25b7ca0999a7d8c16e8392dee70c3
  FBLazyVector: f64d1e2ea739b4d8f7e4740cde18089cd97fe864
  FBReactNativeSpec: 9f2b8b243131565335437dba74923a8d3015e780
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  hermes-engine: 9cecf9953a681df7556b8cc9c74905de8f3293c0
  libaom: 144606b1da4b5915a1054383c3a4459ccdb3c661
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libvmaf: 27f523f1e63c694d14d534cd0fddd2fab0ae8711
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mute: 20135a96076f140cc82bfc8b810e2d6150d8ec7e
  RCT-Folly: cd21f1661364f975ae76b3308167ad66b09f53f5
  RCTRequired: ca1d7414aba0b27efcfa2ccd37637edb1ab77d96
  RCTTypeSafety: 678e344fb976ff98343ca61dc62e151f3a042292
  React: e296bcebb489deaad87326067204eb74145934ab
  React-callinvoker: d0b7015973fa6ccb592bb0363f6bc2164238ab8c
  React-Codegen: a89833d9b3ccafeb8cc7b2e87800aad8903b8990
  React-Core: 3c065b603637303cd150644b633e0b8980d258f1
  React-CoreModules: 069bdb1c6013215afe85fb9627569dc1f21b8f44
  React-cxxreact: a1ec4ab2c051f62ab752b86acb3ab1498d46d82f
  React-debug: d444db402065cca460d9c5b072caab802a04f729
  React-Fabric: 30d61b17f5ec61b8f69e074d359a942c8edb48ed
  React-FabricImage: 52209c0a7828893788d89b342657747fff852e52
  React-graphics: 2d3bb1c23fb71e7dc0f25e1c13f9cd8a0f331166
  React-hermes: 2b8dd8fa2082a7e219cd329ee1c5eec18b82b206
  React-ImageManager: 37eeded59dc2bac8d76c9f78f421980c972215b6
  React-jserrorhandler: 1ddd1e3e596bd3846ad81d2aa7ca3946c15b9ed0
  React-jsi: 84c4e48ed0a563c9e103514ea9572bac486a61a1
  React-jsiexecutor: 6206f71ec0fe8e0d5db321a7f7d91922d310fee9
  React-jsinspector: 85583ef014ce53d731a98c66a0e24496f7a83066
  React-logger: 55764cd993880c6f887505854581c9faf68feff2
  React-Mapbuffer: 15dfcfeb4428d8799cce75e7fe85b18b706029e0
  react-native-keep-awake: 21ff40767cde4bd81021ffee12480aee4b5b91bb
  react-native-safe-area-context: bf9d9d58f0f6726d4a6257088044c2595017579d
  react-native-screen-brightness: 16a7b26d12d89654fc6a2e9ff1e55c1a4a10afcb
  react-native-switch-audio-output: 1879482689f7e783c4292bcde9874dc30c575e69
  react-native-track-player: 12bd55f56b5557bfd9aa59d5284954529874fb57
  react-native-volume-manager: d6af6121bd2939be19f9c959ba7db931860f756f
  React-nativeconfig: b4d4e9901d4cabb57be63053fd2aa6086eb3c85f
  React-NativeModulesApple: 541d64309a3037060cc416db5c8a63ee5884048e
  React-perflogger: 5f49905de275bac07ac7ea7f575a70611fa988f2
  React-RCTActionSheet: 37edf35aeb8e4f30e76c82aab61f12d1b75c04ec
  React-RCTAnimation: b49b2e3beffa553e2120ef0767ce99b4591893c4
  React-RCTAppDelegate: 95e826d48b372cb5c90947c72667f3ce86e77009
  React-RCTBlob: c88cdc50aa116f53353b82d2d394d49e1de47ad3
  React-RCTFabric: b6f90f1bfd1f601e66f4deeb9b21e217c6fb6b69
  React-RCTImage: 665aaf80481423b2e896dcc67afa72e5993a2a4c
  React-RCTLinking: a9321777212cf50b396983b4f3b3190fbfe53aa8
  React-RCTNetwork: 4726e738c784679902d8425bd02c78f7e69d2ebf
  React-RCTSettings: 4831390d89a911f10f154d5c440f6312f8aebe3d
  React-RCTText: 7b1451059ba1d2c40f057c58211864c5e81e90a4
  React-RCTVibration: d23654befc1d9eda8b69b0e9d4127800abcae76f
  React-rendererdebug: ec22f2e3e545bd0ad15abc6e5710595ccfe45c94
  React-rncore: b0a8e1d14dabb7115c7a5b4ec8b9b74d1727d382
  React-runtimeexecutor: bb328dbe2865f3a550df0240df8e2d8c3aaa4c57
  React-runtimescheduler: a01dfb7ca980edebcc7d2d289ca900dea5d7e28b
  React-utils: 288c9cb9a73bb150c273c84df7c2f8546f28e23f
  ReactCommon: 2e5492a3e3a8e72d635c266405e49d12627e5bf0
  RNCAsyncStorage: b44e8a4e798c3e1f56bffccd0f591f674fb9198f
  RNSVG: 6d5ed33b6635ed6d6ecb50744dcf127580c39ed5
  SDWebImage: 750adf017a315a280c60fde706ab1e552a3ae4e9
  SDWebImageAVIFCoder: 8348fef6d0ec69e129c66c9fe4d74fbfbf366112
  SDWebImageSVGCoder: 15a300a97ec1c8ac958f009c02220ac0402e936c
  SDWebImageWebPCoder: af09429398d99d524cae2fe00f6f0f6e491ed102
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  SwiftAudioEx: 83eabba2940924fc1c0d5cb0896049921365229c
  Yoga: 805bf71192903b20fc14babe48080582fee65a80

PODFILE CHECKSUM: c6fbad91ee6ec450646be7cf16978cbf0491545d

COCOAPODS: 1.16.2
