//
//  AdvancedAppStateModule.swift
//  ScreamingFocusExpo
//
//  Created by <PERSON><PERSON><PERSON> on 6/25/23.
//

import Foundation
import UIKit

@objc(AppStateModule)
class AppStateModule: NSObject {
  
  @objc
  static func requiresMainQueueSetup() -> <PERSON><PERSON> {
    return true
  }
  
  @objc
  func startObserving() {
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(appDidEnterBackground),
      name: UIApplication.didEnterBackgroundNotification,
      object: nil
    )
    
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(appWillResignActive),
      name: UIApplication.willResignActiveNotification,
      object: nil
    )
  }
  
  @objc
  func stopObserving() {
    NotificationCenter.default.removeObserver(self)
  }
  
  @objc
  func appDidEnterBackground() {
    sendEvent(withName: "appDidEnterBackground", body: nil)
  }
  
  @objc
  func appWillResignActive() {
    sendEvent(withName: "appWillResignActive", body: nil)
  }
  
  @objc
  func sendEvent(withName name: String, body: Any?) {
    if let bridge = self.bridge {
      bridge.eventDispatcher().sendAppEvent(withName: name, body: body)
    }
  }
  
  deinit {
    stopObserving()
  }
}

