#!/usr/bin/env ruby

require 'xcodeproj'

# Open the Xcode project
project_path = 'ScreamingFocusExpo.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Get the main target
target = project.targets.find { |t| t.name == 'ScreamingFocusExpo' }

# Remove all audio file references from resources build phase
resources_build_phase = target.resources_build_phase
files_to_remove = []

resources_build_phase.files.each do |build_file|
  file_ref = build_file.file_ref
  if file_ref && file_ref.path && file_ref.path.end_with?('.mp3')
    files_to_remove << build_file
    puts "Removing: #{file_ref.path}"
  end
end

files_to_remove.each do |build_file|
  resources_build_phase.remove_file_reference(build_file.file_ref)
end

# Remove Audio groups and file references
main_group = project.main_group.find_subpath('ScreamingFocusExpo')
audio_group = main_group.find_subpath('Audio')
if audio_group
  audio_group.clear
  main_group.children.delete(audio_group)
  puts "Removed Audio group"
end

# Save the project
project.save

puts "Cleaned up Xcode project - removed all audio file references"
