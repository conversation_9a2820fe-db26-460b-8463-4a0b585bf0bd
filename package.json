{"name": "screamingfocusexpo", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "ios-device": "expo run:ios --device", "ios-build": "yarn pod-install && cd ios && xcodebuild -workspace ScreamingFocusExpo.xcworkspace -scheme ScreamingFocusExpo -configuration Debug -destination generic/platform=iOS -allowProvisioningUpdates", "ios-build-device": "yarn pod-install && cd ios && xcodebuild -workspace ScreamingFocusExpo.xcworkspace -scheme ScreamingFocusExpo -configuration Debug -destination 'id=00008110-001C390A3C68401E' -allowProvisioningUpdates", "ios-build-any": "cd ios && xcodebuild -workspace ScreamingFocusExpo.xcworkspace -scheme ScreamingFocusExpo -configuration Debug -destination 'platform=iOS,name=iPhone' -allowProvisioningUpdates", "ios-clean": "cd ios && rm -rf build && xcodebuild clean -workspace ScreamingFocusExpo.xcworkspace -scheme ScreamingFocusExpo", "ios-devices": "xcrun devicectl list devices", "web": "expo start --web", "pod-install": "cd ios && /opt/homebrew/bin/pod install", "pod-update": "cd ios && /opt/homebrew/bin/pod update", "pod-clean": "cd ios && rm -rf Pods Podfile.lock && /opt/homebrew/bin/pod install"}, "dependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/runtime": "^7.27.6", "@react-native-async-storage/async-storage": "^2.2.0", "expo": "~50.0.0", "expo-av": "~13.10.6", "expo-dev-client": "~3.3.0", "expo-image": "~1.10.0", "expo-keep-awake": "~12.8.2", "expo-notifications": "~0.27.0", "expo-screen-orientation": "~6.4.1", "expo-sensors": "~12.9.1", "expo-splash-screen": "~0.26.0", "expo-status-bar": "~1.11.0", "native-base": "^3.4.28", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.73.6", "react-native-keep-awake": "^4.0.0", "react-native-safe-area-context": "^4.8.2", "react-native-screen-brightness": "^2.0.0-alpha", "react-native-simple-events": "^1.0.1", "react-native-svg": "^14.1.0", "react-native-switch-audio-output": "^1.1.2", "react-native-track-player": "^3.2.0", "react-native-volume-manager": "^1.5.1"}, "devDependencies": {"@babel/core": "^7.27.4", "babel-preset-expo": "^13.2.0"}, "private": true}