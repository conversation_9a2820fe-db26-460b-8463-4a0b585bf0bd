# Testing Countdown Timer Implementation

## Quick Test Steps

1. **Build and install** the app on a physical iOS device
2. **Grant Screen Time permissions** when prompted
3. **Press "Test Shield Screens"** button
4. **Try opening blocked apps** to see countdown timer on shield screens
5. **Check main app** for status bar timer display

## Expected Results

### Shield Screen Features
- **Title**: Random casual title ("agggghhhh", "come on", etc.)
- **Icon**: Custom screemio.png image
- **Subtitle**: Sarcastic message + countdown timer
- **Timer Format**: "Time remaining: MM:SS"

### Main App Features
- **Status Bar Timer**: Shows ⏰ MM:SS when session active
- **10-Minute Test**: Test shield button starts 10-minute countdown
- **Auto-Stop**: Session ends automatically after timer expires

## Test Scenarios

### 1. Test Shield Button (10 minutes)
```
Press "Test Shield Screens" → 
10-minute timer starts → 
Try opening Instagram/TikTok → 
See shield with countdown → 
Timer counts down in real-time
```

### 2. Regular Focus Session
```
Set slider to desired time → 
Press "start" → 
Timer starts for selected duration → 
Shield screens show countdown → 
Session ends when timer reaches 00:00
```

### 3. Timer Display Examples
```
Shield Screen:
"Your followers will survive 😒

Time remaining: 09:45"

Main App Status:
"⏰ 09:45"
```

## Implementation Details

### Shared Timer Storage
- Uses App Group: `group.com.screamingfocus.app`
- Stores session start time and duration in UserDefaults
- Shield extension reads timer data to display countdown

### Timer Updates
- Shield screens show real-time countdown
- Main app displays timer in status bar
- Auto-clears when session ends

## Troubleshooting

### If timer doesn't appear:
1. Check App Group entitlements are configured
2. Verify UserDefaults sharing between app and extension
3. Rebuild project to ensure entitlements are applied

### If countdown is wrong:
1. Check timezone handling in timer calculations
2. Verify session start time is stored correctly
3. Test with shorter durations (1-2 minutes)

## Success Criteria

✅ **Shield Timer**: Countdown appears on all blocking screens  
✅ **Status Timer**: Main app shows session time remaining  
✅ **10-Min Test**: Test button creates 10-minute session  
✅ **Real-time Updates**: Timer counts down every second  
✅ **Auto-End**: Session stops when timer reaches 00:00  
✅ **Sarcastic Messages**: Timer appears below category messages  
