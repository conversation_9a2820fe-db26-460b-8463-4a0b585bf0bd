/* eslint-disable react-native/no-inline-styles */
/**
 * Screaming Focus App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useEffect, useState } from 'react';
import { NativeBaseProvider } from 'native-base';
import { View, LogBox, StyleSheet, Dimensions, DeviceEventEmitter } from 'react-native';
import * as Notifications from 'expo-notifications';

import Colors from './src/constants/Colors';
import StyledText from './src/components/StyledText';
import { useFonts } from 'expo-font';
import { theme } from './theme';
import { useKeepAwake } from 'expo-keep-awake';
import NotificationManager from './src/NotificationManager';
import { useBrightnessPulse } from './src/hooks/useBrightnessPulse';

// Import custom components
import HomeScreen from './src/components/HomeScreen';
import FocusScreen from './src/components/FocusScreen';
import QuitAlertModal from './src/components/modals/QuitAlertModal';
import ScreemioStoryModal from './src/components/modals/ScreemioStoryModal';
import SetupModal from './src/components/modals/SetupModal';

// Import custom hooks
import useFocusSession from './src/hooks/useFocusSession';
import useTikTokMonitoring from './src/hooks/useTikTokMonitoring';

const App = () => {
  // Initialize hooks
  const testBrightnessPulse = useBrightnessPulse();
  const {
    focusSessionActive,
    sessionTimeRemaining,
    notificationsScheduled,
    skipCountdown,
    screenTimeAuthorized,
    screenTimeAvailable,
    startFocusSession,
    endFocusSession,
    requestScreenTimePermission,
    handleNotificationResponse
  } = useFocusSession();

  // TikTok monitoring hook
  const {
    isMonitoring: tiktokMonitoring,
    isAvailable: tiktokMonitoringAvailable,
    isAuthorized: tiktokMonitoringAuthorized,
    startMonitoring: startTikTokMonitoring,
    stopMonitoring: stopTikTokMonitoring,
    requestAuthorization: requestTikTokAuthorization,
  } = useTikTokMonitoring();

  // App state
  const [sleepMode, setSleepMode] = useState(false);
  const [sleepTime, setSleepTime] = useState(30);
  const [focuser] = useState('screamio');
  const [showQuitAlert, setShowQuitAlert] = useState(false);
  const [showScreemioStory, setShowScreemioStory] = useState(false);
  const [showSetupModal, setShowSetupModal] = useState(false);

  useKeepAwake();

  // Function to show quit alert
  const quitApp = () => {
    setShowQuitAlert(true);
  };

  // Debug function to test Screen Time
  const debugScreenTime = async () => {
    console.log('=== Screen Time Debug ===');
    // This function is just passed to the HomeScreen for development purposes
  };

  // Debug function to test TikTok monitoring
  const debugTikTokMonitoring = async () => {
    console.log('=== TikTok Monitoring Debug ===');
    console.log('Available:', tiktokMonitoringAvailable);
    console.log('Authorized:', tiktokMonitoringAuthorized);
    console.log('Monitoring:', tiktokMonitoring);

    if (tiktokMonitoringAvailable && tiktokMonitoringAuthorized) {
      if (!tiktokMonitoring) {
        console.log('Starting TikTok monitoring...');
        const result = await startTikTokMonitoring();
        console.log('Start result:', result);
      } else {
        console.log('Testing TikTok usage exceeded...');
        const ScreenTimeModule = await import('./src/ScreenTimeModule');
        const testResult = await ScreenTimeModule.default.testTikTokUsageExceeded();
        console.log('Test result:', testResult);
      }
    } else if (tiktokMonitoringAvailable && !tiktokMonitoringAuthorized) {
      console.log('Requesting TikTok monitoring authorization...');
      const result = await requestTikTokAuthorization();
      console.log('Authorization result:', result);
    }
  };

  // Handle starting a focus session
  const handleStartFocusSession = async () => {
    await startFocusSession(sleepTime);
    setSleepMode(true);
  };

  // Handle ending a focus session
  const handleEndFocusSession = async () => {
    await endFocusSession();
    setSleepMode(false);
  };

  // Handle enabling Screen Time
  const handleEnableScreenTime = () => {
    setShowSetupModal(false);
    requestScreenTimePermission();
  };

  // Handle confirming app quit
  const handleConfirmQuit = () => {
    setShowQuitAlert(false);
    console.log("User chose to be a jerk - Screemio is disappointed");
  };

  useEffect(() => {
    LogBox.ignoreLogs(['Animated: `useNativeDriver`']);

    // Handle notification responses (when user taps notification)
    const notificationResponseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      const result = handleNotificationResponse(response);
      if (result) {
        setSleepMode(true);
      }
    });

    // Add event listeners for test timer
    const startTestTimerListener = DeviceEventEmitter.addListener('startTestTimer', () => {
      // Test timer functionality handled in useFocusSession hook
    });

    const stopTestTimerListener = DeviceEventEmitter.addListener('stopTestTimer', () => {
      // Test timer functionality handled in useFocusSession hook
    });

    return () => {
      // Clean up event listeners
      notificationResponseSubscription.remove();
      startTestTimerListener.remove();
      stopTestTimerListener.remove();
    };
  }, []);

  // Update sleep mode when focus session ends
  useEffect(() => {
    if (!focusSessionActive && sleepMode) {
      setSleepMode(false);
    }
  }, [focusSessionActive]);

  // Initialize TikTok monitoring when app starts
  useEffect(() => {
    const initializeTikTokMonitoring = async () => {
      if (tiktokMonitoringAvailable && tiktokMonitoringAuthorized && !tiktokMonitoring) {
        console.log('Starting TikTok monitoring automatically...');
        const result = await startTikTokMonitoring();
        if (result.success) {
          console.log('TikTok monitoring started successfully');
        } else {
          console.warn('Failed to start TikTok monitoring:', result.message);
        }
      }
    };

    initializeTikTokMonitoring();
  }, [tiktokMonitoringAvailable, tiktokMonitoringAuthorized, tiktokMonitoring, startTikTokMonitoring]);

  const [fontsLoaded] = useFonts({
    CarrolWild: require('./assets/fonts/stamp.ttf'),
  });

  if (!fontsLoaded) {
    return <></>;
  }

  return (
    <NativeBaseProvider theme={theme}>
      <View
        style={{
          ...styles.container,
          backgroundColor: sleepMode ? 'black' : Colors.color2,
        }}
      >
        <View style={{ flex: 1 }}>
          <StyledText
            color="0"
            style={{
              fontSize: 35,
              fontWeight: 700,
              paddingTop: 50,
              textAlign: 'center',
              top: 0,
            }}
          >
            Screaming Focus
          </StyledText>
          <StyledText style={{ fontSize: 20, padding: 20, flex: 1, height: 40 }} color="2">
            -Place your phone face down to stop the screaming
          </StyledText>
        </View>
        
        {sleepMode ? (
          <FocusScreen 
            focusSessionActive={focusSessionActive}
            skipCountdown={skipCountdown}
            onEndSession={handleEndFocusSession}
            focuser={focuser}
          />
        ) : (
          <HomeScreen
            sleepTime={sleepTime}
            setSleepTime={setSleepTime}
            onStartFocusSession={handleStartFocusSession}
            onShowSetupModal={() => setShowSetupModal(true)}
            onShowScreemioStory={() => setShowScreemioStory(true)}
            onQuitApp={quitApp}
            screenTimeAvailable={screenTimeAvailable}
            screenTimeAuthorized={screenTimeAuthorized}
            sessionTimeRemaining={sessionTimeRemaining}
            notificationsScheduled={notificationsScheduled}
            debugScreenTime={debugScreenTime}
            debugTikTokMonitoring={debugTikTokMonitoring}
            tiktokMonitoringAvailable={tiktokMonitoringAvailable}
            tiktokMonitoringAuthorized={tiktokMonitoringAuthorized}
            tiktokMonitoring={tiktokMonitoring}
            testBrightnessPulse={testBrightnessPulse}
            NotificationManager={NotificationManager}
          />
        )}

        {/* Modals */}
        <QuitAlertModal 
          visible={showQuitAlert} 
          onClose={() => setShowQuitAlert(false)} 
          onConfirmQuit={handleConfirmQuit}
        />
        
        <ScreemioStoryModal 
          visible={showScreemioStory} 
          onClose={() => setShowScreemioStory(false)} 
        />
        
        <SetupModal 
          visible={showSetupModal} 
          onClose={() => setShowSetupModal(false)} 
          onEnableScreenTime={handleEnableScreenTime} 
        />
      </View>
    </NativeBaseProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    backgroundColor: Colors.color2,
    flex: 1,
    alignContent: 'center',
    justifyContent: 'center',
  },
});

export default App;