# Shield Extensions Setup Guide

This guide explains how to add custom blocking screens (shield extensions) to the Screaming Focus app using iOS Screen Time API.

## Overview

The shield extensions provide custom blocking screens that appear when users try to access restricted apps during focus sessions. Instead of just preventing app access, users see a branded blocking screen with focus-related messaging and action buttons.

## What You'll Get

- **Custom Blocking Screens**: Branded screens with Screaming Focus messaging
- **No Action Buttons**: Pure blocking screens with no interactive elements
- **Category-Specific Messages**: Different messages for social media, games, etc.
- **Complete Commitment**: Users cannot interact with blocking screens at all

## Manual Setup Required

⚠️ **Important**: The shield extensions require manual setup in Xcode as they are iOS App Extensions that cannot be automatically added via React Native.

### Step 1: Add Shield Configuration Extension

1. **Open Xcode project**: `ios/ScreamingFocusExpo.xcworkspace`

2. **Add new target**:
   - File → New → Target
   - Choose "App Extension" → "Shield Configuration Extension"
   - Product Name: `ScreamingFocusShield`
   - Bundle Identifier: `com.drszav.screamingFocus.shield`
   - Language: Swift

3. **Replace generated files**:
   - Delete the generated `ShieldConfigurationExtension.swift`
   - Copy `ios/ScreamingFocusShield/ShieldConfigurationExtension.swift` to the target
   - Replace `Info.plist` with `ios/ScreamingFocusShield/Info.plist`
   - Add `ios/ScreamingFocusShield/ScreamingFocusShield.entitlements`

4. **Configure target settings**:
   - Deployment Target: iOS 15.0
   - Code Signing: Same as main app
   - Entitlements: `ScreamingFocusShield.entitlements`

### Step 2: Configure App Groups (Optional)

**Note**: App Groups are no longer required since we eliminated shield actions, but you can skip this step entirely.

### Step 3: Update Build Schemes

1. **Edit main app scheme**:
   - Product → Scheme → Edit Scheme
   - Build → Add the shield configuration extension target
   - Ensure extension builds before the main app

2. **Test build**:
   - Clean build folder (Cmd+Shift+K)
   - Build project (Cmd+B)
   - Verify no errors

## File Structure After Setup

```
ios/
├── ScreamingFocusExpo/                    # Main app
│   ├── ScreamingFocusExpo.entitlements   # Updated with Family Controls
│   └── ...
├── ScreamingFocusShield/                  # Shield Configuration Extension
│   ├── ShieldConfigurationExtension.swift
│   ├── Info.plist
│   └── ScreamingFocusShield.entitlements
└── ScreamingFocusExpo.xcworkspace
```

## Testing the Shield Extensions

1. **Build and install** the app on a physical iOS device (iOS 15.0+)

2. **Grant Screen Time permissions** when prompted

3. **Start a focus session** in the app

4. **Try to open a blocked app** (social media, games, etc.)

5. **Verify custom blocking screen** appears with:
   - "🎯 Screaming Focus" title
   - Focus-related messaging
   - No interactive buttons

6. **Test user experience**:
   - Users see the blocking screen but cannot interact with it
   - Must use home button or gesture to return to home screen
   - Focus session continues running

## Customization Options

### Modify Blocking Messages

Edit `ShieldConfigurationExtension.swift`:

```swift
title: ShieldConfiguration.Label(
    text: "Your Custom Title",
    color: UIColor.label
),
subtitle: ShieldConfiguration.Label(
    text: "Your custom blocking message here.",
    color: UIColor.secondaryLabel
)
```

### Remove All Buttons (Current Implementation)

The current implementation has no buttons for maximum focus commitment:

```swift
// No primaryButtonLabel or secondaryButtonLabel specified
// Users see only the title, subtitle, and icon
```

### Update Icons

```swift
icon: UIImage(systemName: "your.custom.icon"),
```

## Troubleshooting

### Extensions Not Building

- Verify all targets have correct bundle identifiers
- Check that entitlements files are properly assigned
- Ensure App Groups are enabled for all targets

### Blocking Screens Not Appearing

- Confirm Screen Time authorization is granted
- Verify focus session is active in main app
- Check that apps being tested are in blocked categories

### Blocking Screens Have Buttons (Unintended)

- Verify you removed all `primaryButtonLabel` and `secondaryButtonLabel` from ShieldConfiguration
- Check that no shield action extension is included in the project
- Rebuild the project to ensure changes take effect

### Build Errors

- Clean build folder and rebuild
- Verify iOS deployment target is 15.0+ for all targets
- Check that all required frameworks are linked

## Advanced Configuration

### Custom App Selection

To allow users to select specific apps to block, implement the FamilyActivityPicker in the main app and pass selections to the ScreenTimeManager.

### Dynamic Messages

Modify the shield extensions to read custom messages from UserDefaults shared via App Groups.

### Analytics

Add tracking to shield actions to measure focus session effectiveness.

## Support

For issues with shield extensions:

1. Check Xcode console for error messages
2. Verify all setup steps were completed
3. Test on physical iOS device (required for Screen Time)
4. Ensure iOS 15.0+ and valid developer account
