/**
 * Utility functions for accelerometer data processing
 */

/**
 * Determines if the phone is face down based on accelerometer data
 * @param {number} x - X-axis acceleration
 * @param {number} y - Y-axis acceleration  
 * @param {number} z - Z-axis acceleration
 * @returns {boolean} True if phone is face down
 */
export const isPhoneFaceDown = (x, y, z) => {
  return Math.abs(z - 1) < 0.1 && Math.abs(x) < 0.1 && Math.abs(y) < 0.1;
};

/**
 * Calculates scream volume based on accelerometer data
 * @param {number} x - X-axis acceleration
 * @param {number} y - Y-axis acceleration
 * @param {number} z - Z-axis acceleration
 * @returns {number} Volume between 0.5 and 1.0
 */
export const calculateScreamVolume = (x, y, z) => {
  let screamVolume = Math.pow(z - 0.8, 2) + Math.pow(x - 0.1, 2) + Math.pow(y - 0.1, 2);
  screamVolume = screamVolume / 2;
  screamVolume = Math.min(screamVolume, 1);
  screamVolume = Math.max(screamVolume, 0.1);
  return Math.round(screamVolume * 100) / 100;
};

/**
 * Determines the appropriate accelerometer update interval based on phone state
 * @param {boolean} isFaceDown - Whether phone is face down
 * @returns {number} Update interval in milliseconds
 */
export const getAccelerometerInterval = (isFaceDown) => {
  return isFaceDown ? 500 : 200; // Slower updates when face down
};
