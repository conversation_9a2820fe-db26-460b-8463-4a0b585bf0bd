import { Audio } from 'expo-av';
import * as screamio from '../assets/sounds/screamio';
import { Vibration, AppState, Platform } from 'react-native';
import RNSwitchAudioOutput from 'react-native-switch-audio-output';
import { VolumeManager } from 'react-native-volume-manager';
import ScreenTimeModule from './ScreenTimeModule';
import NativeAudioManager from './NativeAudioManager';
const PATTERN = [100, 50, 100];

export class ScreamAudio {
  constructor(focuser) {
    this.initializeAudio();
    this.sound = new Audio.Sound(); // Keep for countdown sound
    this.isScreaming = false;
    this.focusModeActive = false;
    this.appState = AppState.currentState;
    this.backgroundScreamingEnabled = false;
    this.useNativeAudio = Platform.OS === 'ios'; // Use native audio on iOS

    if (focuser) {
      this.audioStopped = false;
      this.focuser = focuser;

      // Initialize native audio manager on iOS
      if (this.useNativeAudio) {
        this.initializeNativeAudio();
      } else {
        // Keep existing Expo AV setup for Android
        this.sound.setOnPlaybackStatusUpdate((status) => {
          if (!status.didJustFinish) return;
          this.sound.unloadAsync();
          if (this.isScreaming) {
            this.playRandomScream();
          }
        });
      }

      // Add app state change listener
      this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));
    }
  }

  async initializeAudio() {
    RNSwitchAudioOutput.selectAudioOutput(RNSwitchAudioOutput.AUDIO_SPEAKER);
    await VolumeManager.setVolume(1);
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        // Enable background audio playback
        staysActiveInBackground: true,
      });
    } catch (err) {
      console.log(err);
    }
  }

  async initializeNativeAudio() {
    try {
      console.log('Attempting to initialize native audio manager...');
      await NativeAudioManager.initialize();
      if (this.focuser) {
        console.log('Setting focuser to:', this.focuser);
        NativeAudioManager.setFocuser(this.focuser);
      }
      console.log('Native audio manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize native audio manager:', error);
      console.log('Falling back to Expo AV');
      // Fallback to Expo AV if native fails
      this.useNativeAudio = false;
    }
  }

  setVolume(volume) {
    this.sound.setVolume(volume);
  }
  setSpeed(speed) {
    this.sound.setSpeed(speed);
  }

  handleAppStateChange(nextAppState) {
    console.log('App state changed from', this.appState, 'to', nextAppState);

    if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      console.log('App came to foreground');
    } else if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
      // App has gone to the background
      console.log('App went to background');
      if (this.isScreaming) {
        console.log('Enabling background screaming');
        this.backgroundScreamingEnabled = true;
        // Continue screaming in background
        this.continueBackgroundScreaming();
      }
    }

    this.appState = nextAppState;
  }

  async continueBackgroundScreaming() {
    // Ensure audio continues playing in background
    if (this.backgroundScreamingEnabled && this.isScreaming) {
      console.log('Continuing screaming in background');
      // The audio should continue automatically due to background audio mode
      // But we can add additional logic here if needed
    }
  }

  async startScream(volume) {
    console.log('start scream with calculated volume:', volume);

    // DO NOT set system volume here - only set it once at the beginning of focus session
    // The volume parameter is for audio player volume, not system volume

    if (!this.isScreaming) {
      try {
        if (!this.focuser) {
          throw new Error('focuser not selected!');
        }

        Vibration.vibrate(PATTERN, true);
        this.isScreaming = true;
        this.audioStopped = false; // Reset audio stopped flag

        console.log('start scream');

        if (this.useNativeAudio) {
          // Use native audio manager for iOS - always use maximum volume for impact
          console.log('Using native audio manager for screaming at MAX volume');
          NativeAudioManager.setVolume(1.0); // Always use maximum volume for iOS
          NativeAudioManager.startScreaming();
        } else {
          // Use Expo AV for Android - use calculated volume
          console.log('Using Expo AV for screaming at volume:', volume);
          this.sound.setVolume(volume);
          await this.playRandomScream();
        }
      } catch (error) {
        console.log(error);
        // An error occurred!
      }
    } else {
      // If already screaming, update the volume for both platforms
      if (this.useNativeAudio) {
        // Update volume for native audio (iOS)
        NativeAudioManager.setVolume(volume);
      } else {
        // Update volume for Expo AV (Android)
        this.sound.setVolume(volume);
      }
    }
  }

  async startCountdown() {
    const sound = new Audio.Sound();
    try {
      await VolumeManager.setVolume(0.5);
      await sound.loadAsync(require('../assets/sounds/countdown.mp3'));
      await sound.playAsync();
    } catch (error) {
      console.error(error);
    }
  }

  async playRandomScream() {
    console.log('play random scream');

    // This method is now only used for Android (Expo AV)
    // iOS uses the native audio manager
    if (this.useNativeAudio) {
      console.log('Using native audio manager - this method should not be called');
      return;
    }

    try {
      const clipNumber = Math.floor(Math.random() * 9 + 1);
      await this.sound.loadAsync(screamio['voz' + clipNumber], {
        shouldPlay: false,
      });

      await this.sound.playAsync();
      this.audioStopped = false;
    } catch (e) {
      console.error('Error playing random scream:', e);
    }
  }

  async stopScream() {
    console.log('stop scream');
    this.audioStopped = true;
    this.backgroundScreamingEnabled = false; // Disable background screaming

    if (this.isScreaming) {
      // DO NOT set system volume to 0 here - only restore it when focus session ends
      try {
        this.isScreaming = false;
        Vibration.cancel();

        if (this.useNativeAudio) {
          // Use native audio manager for iOS
          NativeAudioManager.stopScreaming();
        } else {
          // Use Expo AV for Android
          await this.sound.stopAsync();
          await this.sound.unloadAsync();
        }
      } catch (e) {
        console.error('Error stopping scream:', e);
      }
    }
  }

  // Method to set system volume to maximum at start of focus session
  async setMaxSystemVolume() {
    console.log('Setting system volume to maximum for focus session');
    await VolumeManager.setVolume(1.0);
  }

  // Method to restore system volume when focus session ends
  async restoreSystemVolume(originalVolume = 0.5) {
    console.log('Restoring system volume to:', originalVolume);
    await VolumeManager.setVolume(originalVolume);
  }

  // Screen Time integration methods
  async startFocusMode() {
    try {
      if (!this.focusModeActive) {
        console.log('Starting Screen Time focus mode...');
        const result = await ScreenTimeModule.startFocusMode();
        if (result.success) {
          this.focusModeActive = true;
          console.log('Screen Time focus mode activated');
        } else {
          console.warn('Failed to activate Screen Time focus mode:', result.message);
        }
      }
    } catch (error) {
      console.error('Error starting Screen Time focus mode:', error);
    }
  }

  async stopFocusMode() {
    try {
      if (this.focusModeActive) {
        console.log('Stopping Screen Time focus mode...');
        const result = await ScreenTimeModule.stopFocusMode();
        if (result.success) {
          this.focusModeActive = false;
          console.log('Screen Time focus mode deactivated');
        } else {
          console.warn('Failed to deactivate Screen Time focus mode:', result.message);
        }
      }
    } catch (error) {
      console.error('Error stopping Screen Time focus mode:', error);
    }
  }

  async requestScreenTimePermission() {
    try {
      console.log('Requesting Screen Time authorization...');
      const result = await ScreenTimeModule.requestAuthorization();
      console.log('Screen Time authorization result:', result);
      return result;
    } catch (error) {
      console.error('Error requesting Screen Time authorization:', error);
      throw error;
    }
  }

  async getScreenTimeStatus() {
    try {
      const authStatus = await ScreenTimeModule.getAuthorizationStatus();
      const focusStatus = await ScreenTimeModule.isFocusModeActive();
      return {
        authorized: authStatus.status === 'approved',
        focusModeActive: focusStatus.isActive
      };
    } catch (error) {
      console.error('Error getting Screen Time status:', error);
      return {
        authorized: false,
        focusModeActive: false
      };
    }
  }

  // Cleanup method to remove listeners
  cleanup() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    this.stopScream();
  }
}
