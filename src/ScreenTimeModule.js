import { NativeModules, NativeEventEmitter, Platform } from 'react-native';

const { ScreenTimeManager } = NativeModules;

class ScreenTimeModule {
  constructor() {
    this.eventEmitter = null;
    this.listeners = new Map();
    
    if (Platform.OS === 'ios' && ScreenTimeManager) {
      this.eventEmitter = new NativeEventEmitter(ScreenTimeManager);
    }
  }

  /**
   * Request authorization for Screen Time controls
   * @returns {Promise<Object>} Authorization result
   */
  async requestAuthorization() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      throw new Error('Screen Time is only available on iOS');
    }
    
    try {
      const result = await ScreenTimeManager.requestAuthorization();
      return result;
    } catch (error) {
      console.error('Failed to request Screen Time authorization:', error);
      throw error;
    }
  }

  /**
   * Get current authorization status
   * @returns {Promise<Object>} Current authorization status
   */
  async getAuthorizationStatus() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      return { status: 'unavailable' };
    }
    
    try {
      const result = await ScreenTimeManager.getAuthorizationStatus();
      return result;
    } catch (error) {
      console.error('Failed to get authorization status:', error);
      throw error;
    }
  }

  /**
   * Start focus mode - blocks distracting apps
   * @returns {Promise<Object>} Result of starting focus mode
   */
  async startFocusMode() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('Screen Time blocking is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }
    
    try {
      const result = await ScreenTimeManager.startFocusMode();
      console.log('Focus mode started:', result);
      return result;
    } catch (error) {
      console.error('Failed to start focus mode:', error);
      throw error;
    }
  }

  /**
   * Stop focus mode - removes app restrictions
   * @returns {Promise<Object>} Result of stopping focus mode
   */
  async stopFocusMode() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('Screen Time blocking is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }
    
    try {
      const result = await ScreenTimeManager.stopFocusMode();
      console.log('Focus mode stopped:', result);
      return result;
    } catch (error) {
      console.error('Failed to stop focus mode:', error);
      throw error;
    }
  }

  /**
   * Check if focus mode is currently active
   * @returns {Promise<Object>} Focus mode status
   */
  async isFocusModeActive() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      return { isActive: false };
    }
    
    try {
      const result = await ScreenTimeManager.isFocusModeActive();
      return result;
    } catch (error) {
      console.error('Failed to check focus mode status:', error);
      throw error;
    }
  }

  /**
   * Open app selection interface
   * @returns {Promise<Object>} App selection result
   */
  async selectAppsToBlock() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      throw new Error('App selection is only available on iOS');
    }
    
    try {
      const result = await ScreenTimeManager.selectAppsToBlock();
      return result;
    } catch (error) {
      console.error('Failed to select apps:', error);
      throw error;
    }
  }

  /**
   * Add event listener for Screen Time events
   * @param {string} eventName - Name of the event
   * @param {Function} listener - Event listener function
   */
  addEventListener(eventName, listener) {
    if (!this.eventEmitter) {
      console.warn('Screen Time events are only available on iOS');
      return;
    }

    const subscription = this.eventEmitter.addListener(eventName, listener);
    this.listeners.set(eventName, subscription);
    return subscription;
  }

  /**
   * Remove event listener
   * @param {string} eventName - Name of the event
   */
  removeEventListener(eventName) {
    const subscription = this.listeners.get(eventName);
    if (subscription) {
      subscription.remove();
      this.listeners.delete(eventName);
    }
  }

  /**
   * Remove all event listeners
   */
  removeAllListeners() {
    this.listeners.forEach((subscription) => {
      subscription.remove();
    });
    this.listeners.clear();
  }

  /**
   * Set focus session timer for countdown display
   * @param {number} startTime - Session start time (timestamp)
   * @param {number} duration - Session duration in seconds
   * @returns {Promise<Object>} Result of setting timer
   */
  async setFocusSessionTimer(startTime, duration) {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('Screen Time timer is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }

    try {
      const result = await ScreenTimeManager.setFocusSessionTimer(startTime, duration);
      console.log('Focus session timer set:', result);
      return result;
    } catch (error) {
      console.error('Failed to set focus session timer:', error);
      throw error;
    }
  }

  /**
   * Clear focus session timer
   * @returns {Promise<Object>} Result of clearing timer
   */
  async clearFocusSessionTimer() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('Screen Time timer is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }

    try {
      const result = await ScreenTimeManager.clearFocusSessionTimer();
      console.log('Focus session timer cleared:', result);
      return result;
    } catch (error) {
      console.error('Failed to clear focus session timer:', error);
      throw error;
    }
  }

  /**
   * Check if Screen Time is available on this device
   * @returns {boolean} Availability status
   */
  isAvailable() {
    return Platform.OS === 'ios' && ScreenTimeManager !== null;
  }

  // MARK: - TikTok Usage Monitoring

  /**
   * Start monitoring TikTok usage
   * @returns {Promise<Object>} Result of starting monitoring
   */
  async startTikTokMonitoring() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('TikTok monitoring is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }

    try {
      const result = await ScreenTimeManager.startTikTokMonitoring();
      console.log('TikTok monitoring started:', result);
      return result;
    } catch (error) {
      console.error('Failed to start TikTok monitoring:', error);
      throw error;
    }
  }

  /**
   * Stop monitoring TikTok usage
   * @returns {Promise<Object>} Result of stopping monitoring
   */
  async stopTikTokMonitoring() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('TikTok monitoring is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }

    try {
      const result = await ScreenTimeManager.stopTikTokMonitoring();
      console.log('TikTok monitoring stopped:', result);
      return result;
    } catch (error) {
      console.error('Failed to stop TikTok monitoring:', error);
      throw error;
    }
  }

  /**
   * Check if TikTok monitoring is currently active
   * @returns {Promise<Object>} Monitoring status
   */
  async isTikTokMonitoringActive() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      return { isActive: false };
    }

    try {
      const result = await ScreenTimeManager.isTikTokMonitoringActive();
      return result;
    } catch (error) {
      console.error('Failed to check TikTok monitoring status:', error);
      throw error;
    }
  }

  /**
   * Add listener for TikTok usage exceeded events
   * @param {Function} callback - Function to call when TikTok usage exceeds limit
   * @returns {Function} Unsubscribe function
   */
  addTikTokUsageListener(callback) {
    if (!this.eventEmitter) {
      console.warn('Event emitter not available');
      return () => {};
    }

    const subscription = this.eventEmitter.addListener('onTikTokUsageExceeded', callback);
    const listenerId = Date.now().toString();
    this.listeners.set(listenerId, subscription);

    return () => {
      subscription.remove();
      this.listeners.delete(listenerId);
    };
  }

  /**
   * Remove all TikTok usage listeners
   */
  removeAllTikTokUsageListeners() {
    this.listeners.forEach((subscription) => {
      subscription.remove();
    });
    this.listeners.clear();
  }

  /**
   * Test method to manually trigger TikTok usage exceeded event
   * @returns {Promise<Object>} Result of test trigger
   */
  async testTikTokUsageExceeded() {
    if (Platform.OS !== 'ios' || !ScreenTimeManager) {
      console.warn('TikTok monitoring test is only available on iOS');
      return { success: false, message: 'Not available on this platform' };
    }

    try {
      const result = await ScreenTimeManager.testTikTokUsageExceeded();
      console.log('TikTok usage exceeded test triggered:', result);
      return result;
    } catch (error) {
      console.error('Failed to trigger TikTok usage exceeded test:', error);
      throw error;
    }
  }

}

export default new ScreenTimeModule();
