import { NativeModules, Platform } from 'react-native';

const { AudioManager } = NativeModules;

class NativeAudioManager {
  constructor() {
    this.isInitialized = false;
    this.isAvailable = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Check if AudioManager native module is available
      if (!AudioManager) {
        console.error('AudioManager native module not found');
        throw new Error('AudioManager native module not available');
      }

      console.log('AudioManager native module found:', AudioManager);
      this.isAvailable = true;
      this.isInitialized = true;
      console.log('Native AudioManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Native AudioManager:', error);
      this.isAvailable = false;
      throw error;
    }
  }

  setFocuser(focuser) {
    if (!this.isInitialized || !this.isAvailable) {
      console.warn('AudioManager not initialized or not available');
      return;
    }

    try {
      AudioManager.setFocuser(focuser);
      console.log(`Native AudioManager focuser set to: ${focuser}`);
    } catch (error) {
      console.error('Failed to set focuser:', error);
    }
  }

  startScreaming() {
    if (!this.isInitialized || !this.isAvailable) {
      console.warn('AudioManager not initialized or not available');
      return;
    }

    try {
      AudioManager.startScreaming();
      console.log('Native AudioManager started screaming');
    } catch (error) {
      console.error('Failed to start screaming:', error);
    }
  }

  stopScreaming() {
    if (!this.isInitialized) {
      console.warn('AudioManager not initialized');
      return;
    }
    
    try {
      AudioManager.stopScreaming();
      console.log('Native AudioManager stopped screaming');
    } catch (error) {
      console.error('Failed to stop screaming:', error);
    }
  }

  async isCurrentlyScreaming() {
    if (!this.isInitialized) {
      console.warn('AudioManager not initialized');
      return false;
    }
    
    return new Promise((resolve, reject) => {
      try {
        AudioManager.isCurrentlyScreaming((isScreaming) => {
          resolve(isScreaming);
        });
      } catch (error) {
        console.error('Failed to check screaming status:', error);
        reject(error);
      }
    });
  }

  setVolume(volume) {
    if (!this.isInitialized) {
      console.warn('AudioManager not initialized');
      return;
    }

    try {
      console.log(`📢 Setting native audio volume to: ${volume}`);
      AudioManager.setVolume(volume);
    } catch (error) {
      console.error('Failed to set volume:', error);
    }
  }

  setSpeed(speed) {
    if (!this.isInitialized) {
      console.warn('AudioManager not initialized');
      return;
    }
    
    try {
      AudioManager.setSpeed(speed);
    } catch (error) {
      console.error('Failed to set speed:', error);
    }
  }
}

export default new NativeAudioManager();
