import { useState, useEffect } from 'react';
import ScreenTimeModule from '../ScreenTimeModule';
import NotificationManager from '../NotificationManager';
import { ScreamAudio } from '../screamEngine';

/**
 * Hook to manage focus session logic
 */
const useFocusSession = () => {
  // Focus session state
  const [focusSessionActive, setFocusSessionActive] = useState(false);
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [sessionDuration, setSessionDuration] = useState(null);
  const [sessionTimeRemaining, setSessionTimeRemaining] = useState('');
  const [notificationsScheduled, setNotificationsScheduled] = useState(false);
  const [skipCountdown, setSkipCountdown] = useState(false);
  const [countdown] = useState(() => new ScreamAudio());

  // Screen Time state
  const [screenTimeAuthorized, setScreenTimeAuthorized] = useState(false);
  const [screenTimeAvailable, setScreenTimeAvailable] = useState(false);

  // Initialize Screen Time and check for stored focus session
  useEffect(() => {
    // Check Screen Time availability and authorization
    const initializeScreenTime = async () => {
      const available = ScreenTimeModule.isAvailable();
      console.log('Screen Time available:', available);
      setScreenTimeAvailable(available);

      if (available) {
        try {
          const status = await ScreenTimeModule.getAuthorizationStatus();
          console.log('Initial Screen Time status:', status);
          const authorized = status.status === 'approved';
          setScreenTimeAuthorized(authorized);
          console.log('Screen Time authorized:', authorized);
        } catch (error) {
          console.error('Failed to check Screen Time authorization:', error);
        }
      }
    };

    // Initialize notifications and check for stored focus session
    const initializeNotifications = async () => {
      // Check if there's a stored focus session from a previous app launch
      const storedSession = await NotificationManager.getStoredFocusSession();
      if (storedSession) {
        const { sessionStartTime: startTime, sessionDuration: duration } = storedSession;
        const isActive = NotificationManager.isFocusSessionActive(startTime, duration);

        if (isActive) {
          console.log('Restoring focus session from notification launch');
          setSessionStartTime(startTime);
          setSessionDuration(duration);
          setFocusSessionActive(true);
          setNotificationsScheduled(true);

          // Set timer to end the session at the correct time
          const remainingTime = (startTime + duration * 1000) - Date.now();
          if (remainingTime > 0) {
            setTimeout(async () => {
              await endFocusSession();
            }, remainingTime);
          }
        } else {
          // Session expired, clear stored data
          await NotificationManager.clearAllNotifications();
        }
      }
    };

    initializeScreenTime();
    initializeNotifications();

    // Cleanup Screen Time focus mode if app is closed during session
    return () => {
      if (focusSessionActive) {
        ScreenTimeModule.stopFocusMode().catch(console.error);
      }
    };
  }, []);

  // Update timer every second when session is active
  useEffect(() => {
    let interval;
    if (sessionStartTime && sessionDuration) {
      interval = setInterval(updateSessionTimer, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [sessionStartTime, sessionDuration]);

  // Update the session timer display
  const updateSessionTimer = () => {
    if (!sessionStartTime || !sessionDuration) {
      setSessionTimeRemaining('');
      return;
    }

    const elapsed = (new Date().getTime() - sessionStartTime) / 1000; // Convert to seconds
    const remaining = Math.max(0, sessionDuration - elapsed);

    if (remaining <= 0) {
      setSessionTimeRemaining('');
      setSessionStartTime(null);
      setSessionDuration(null);
      return;
    }

    // Round up to nearest minute
    const minutesLeft = Math.ceil(remaining / 60);

    if (minutesLeft === 1) {
      setSessionTimeRemaining('(1 minute left)');
    } else {
      setSessionTimeRemaining(`(${minutesLeft} minutes left)`);
    }
  };

  // Request Screen Time permissions
  const requestScreenTimePermission = async () => {
    if (!screenTimeAvailable) {
      console.warn('Screen Time is not available on this device');
      return;
    }

    try {
      const result = await ScreenTimeModule.requestAuthorization();
      if (result.status === 'authorized') {
        setScreenTimeAuthorized(true);
        console.log('Screen Time authorization granted');
      } else {
        console.log('Screen Time authorization denied or failed');
      }
    } catch (error) {
      console.error('Failed to request Screen Time authorization:', error);
    }
  };

  // Start a focus session
  const startFocusSession = async (sleepTime) => {
    try {
      // Reset skip countdown flag for new sessions
      setSkipCountdown(false);

      // Request notification permissions first
      const notificationPermission = await NotificationManager.requestPermissions();
      if (!notificationPermission) {
        console.warn('Notification permissions not granted');
      }

      // Start countdown sound
      countdown.startCountdown();

      // Set timer for session
      const duration = sleepTime * 60; // Convert to seconds
      const startTime = new Date().getTime();

      // Set local timer state for status bar
      setSessionStartTime(startTime);
      setSessionDuration(duration);

      // Set timer for shield screens
      await ScreenTimeModule.setFocusSessionTimer(startTime, duration);

      // Start monitoring for background app state to schedule notifications
      if (notificationPermission) {
        console.log('Starting focus mode monitoring for notifications...');
        NotificationManager.startFocusModeMonitoring(startTime, duration);
        setNotificationsScheduled(true);
      }

      // Start Screen Time focus mode immediately if available and authorized
      if (ScreenTimeModule.isAvailable()) {
        try {
          const status = await ScreenTimeModule.getAuthorizationStatus();
          console.log('Screen Time authorization status:', status);
          if (status.status === 'approved') {
            console.log('Starting Screen Time focus mode for entire session...');
            const result = await ScreenTimeModule.startFocusMode();
            console.log('Screen Time startFocusMode result:', result);
            if (result.success) {
              setFocusSessionActive(true);
              console.log('Screen Time focus mode activated for session');

              // Verify focus mode is actually active
              const activeStatus = await ScreenTimeModule.isFocusModeActive();
              console.log('Focus mode active status:', activeStatus);
            } else {
              console.warn('Failed to activate Screen Time focus mode:', result.message);
            }
          } else {
            console.warn('Screen Time not authorized. Status:', status.status);
          }
        } catch (error) {
          console.error('Failed to check Screen Time authorization:', error);
        }
      } else {
        console.warn('Screen Time module not available');
      }

      // Set timer to end the session
      setTimeout(async () => {
        // End the focus session
        await endFocusSession();
      }, sleepTime * 1000 * 60);

    } catch (error) {
      console.error('Failed to start focus session:', error);
    }
  };

  // End a focus session
  const endFocusSession = async () => {
    try {
      // Stop focus mode monitoring and clear notifications
      console.log('Stopping focus mode monitoring...');
      NotificationManager.stopFocusModeMonitoring();
      setNotificationsScheduled(false);

      // Clear session timer
      await ScreenTimeModule.clearFocusSessionTimer();
      setSessionTimeRemaining('');
      setSessionStartTime(null);
      setSessionDuration(null);

      // Stop Screen Time focus mode
      if (focusSessionActive) {
        console.log('Ending Screen Time focus mode...');
        const result = await ScreenTimeModule.stopFocusMode();
        if (result.success) {
          setFocusSessionActive(false);
          console.log('Screen Time focus mode deactivated');
        } else {
          console.warn('Failed to deactivate Screen Time focus mode:', result.message);
        }
      }

      // Reset skip countdown flag
      setSkipCountdown(false);

    } catch (error) {
      console.error('Failed to end focus session:', error);
      // Still clean up state even if Screen Time fails
      setFocusSessionActive(false);
      setSessionTimeRemaining('');
      setSessionStartTime(null);
      setSessionDuration(null);
      setNotificationsScheduled(false);
      setSkipCountdown(false);
    }
  };

  // Handle notification responses (when user taps notification)
  const handleNotificationResponse = (response) => {
    console.log('Notification tapped:', response);
    const result = NotificationManager.handleNotificationResponse(response);

    if (result && result.shouldOpenFocusMode) {
      console.log('Opening focus mode from notification - skipping countdown');
      setSessionStartTime(result.sessionStartTime);
      setSessionDuration(result.sessionDuration);
      setFocusSessionActive(true);
      setNotificationsScheduled(true);
      setSkipCountdown(true); // Skip countdown when returning from notification
      return true;
    }
    return false;
  };

  return {
    // Session state
    focusSessionActive,
    sessionTimeRemaining,
    notificationsScheduled,
    skipCountdown,
    
    // Screen Time state
    screenTimeAuthorized,
    screenTimeAvailable,
    
    // Methods
    startFocusSession,
    endFocusSession,
    requestScreenTimePermission,
    handleNotificationResponse,
    setSkipCountdown
  };
};

export default useFocusSession;