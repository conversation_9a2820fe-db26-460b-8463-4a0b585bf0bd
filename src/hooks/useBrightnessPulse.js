import { useState, useCallback, useRef } from 'react';
import ScreenBrightness from 'react-native-screen-brightness';

/**
 * Custom hook for managing brightness pulsing effects using requestAnimationFrame
 * More efficient than setInterval for smooth 60fps animations
 * @returns {Object} Brightness control functions
 */
export const useBrightnessPulse = () => {
  const [isActive, setIsActive] = useState(false);
  const animationFrameRef = useRef(null);
  const startTimeRef = useRef(null);
  const isRunningRef = useRef(false);

  const startBrightnessPulse = useCallback(() => {
    console.log('Starting INTENSE brightness pulse with animation frames');

    // Prevent multiple instances
    if (isRunningRef.current) {
      console.log('Brightness pulse already running');
      return;
    }

    // Stop any existing animation
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    setIsActive(true);
    isRunningRef.current = true;
    startTimeRef.current = performance.now();

    const animate = (currentTime) => {
      // Exit if stopped
      if (!isRunningRef.current) {
        return;
      }

      // Calculate elapsed time since start
      const elapsed = currentTime - startTimeRef.current;

      try {
        // Use sine wave for smooth pulsing (15x faster cycle for MAXIMUM INTENSITY)
        const cycleSpeed = 0.030; // Increased 50% from 0.020 to 0.030 for even faster pulsing
        const phase = elapsed * cycleSpeed;

        // Create intense pulsing between 0.1 and 1.0 with smooth sine wave
        const brightness = 0.1 + (0.9 * (Math.sin(phase) + 1) / 2);

        // Set brightness (async but don't await to maintain 60fps)
        ScreenBrightness.setBrightness(brightness).catch(error => {
          console.error('Error setting brightness:', error);
        });

      } catch (error) {
        console.error('Error in brightness animation:', error);
      }

      // Continue animation if still running
      if (isRunningRef.current) {
        animationFrameRef.current = requestAnimationFrame(animate);
      }
    };

    // Start the animation loop
    animationFrameRef.current = requestAnimationFrame(animate);
  }, []);

  const stopBrightnessPulse = useCallback(async () => {
    console.log('Stopping brightness pulse');

    // Mark as stopped
    isRunningRef.current = false;
    setIsActive(false);

    // Cancel animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Restore normal brightness
    try {
      await ScreenBrightness.setBrightness(0.5);
    } catch (error) {
      console.error('Error restoring brightness:', error);
    }
  }, []);

  // Cleanup function for component unmount
  const cleanup = useCallback(() => {
    console.log('Cleaning up brightness pulse');
    isRunningRef.current = false;
    setIsActive(false);

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
  }, []);

  return {
    startBrightnessPulse,
    stopBrightnessPulse,
    cleanup,
    isActive,
  };
};
