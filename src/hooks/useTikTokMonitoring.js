import { useState, useEffect, useCallback } from 'react';
import ScreenTimeModule from '../ScreenTimeModule';
import NotificationManager from '../NotificationManager';

/**
 * Custom hook for TikTok usage monitoring
 * Handles starting/stopping monitoring and responding to usage events
 */
const useTikTokMonitoring = () => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Check availability and authorization on mount
  useEffect(() => {
    const checkAvailability = async () => {
      const available = ScreenTimeModule.isAvailable();
      setIsAvailable(available);

      if (available) {
        try {
          const status = await ScreenTimeModule.getAuthorizationStatus();
          setIsAuthorized(status.status === 'approved');
        } catch (error) {
          console.error('Failed to check Screen Time authorization:', error);
          setIsAuthorized(false);
        }
      }
    };

    checkAvailability();
  }, []);

  // Check if monitoring is already active on mount
  useEffect(() => {
    const checkMonitoringStatus = async () => {
      if (isAvailable && isAuthorized) {
        try {
          const status = await ScreenTimeModule.isTikTokMonitoringActive();
          setIsMonitoring(status.isActive);
        } catch (error) {
          console.error('Failed to check TikTok monitoring status:', error);
        }
      }
    };

    checkMonitoringStatus();
  }, [isAvailable, isAuthorized]);

  // Handle TikTok usage exceeded events
  const handleTikTokUsageExceeded = useCallback(async (event) => {
    console.log('TikTok usage exceeded!', event);

    try {
      // Send immediate screaming notification
      await NotificationManager.sendTikTokUsageNotification();

      // Schedule repeating notifications to really get their attention
      await NotificationManager.scheduleRepeatingTikTokNotifications();

      console.log('TikTok usage notifications sent');
    } catch (error) {
      console.error('Failed to send TikTok usage notifications:', error);
    }
  }, []);

  // Poll for TikTok usage exceeded flag in UserDefaults
  useEffect(() => {
    if (!isAvailable || !isAuthorized || !isMonitoring) {
      return;
    }

    const checkTikTokUsage = async () => {
      try {
        // Check if TikTok usage exceeded flag is set
        const userDefaults = await import('react-native').then(rn => rn.NativeModules.UserDefaults);
        // Note: This is a simplified approach - in a real implementation,
        // you might want to use AsyncStorage or a native module to check UserDefaults

        // For now, we'll rely on the event listener approach
      } catch (error) {
        console.error('Error checking TikTok usage:', error);
      }
    };

    // Check every 5 seconds when monitoring is active
    const interval = setInterval(checkTikTokUsage, 5000);

    return () => {
      clearInterval(interval);
    };
  }, [isAvailable, isAuthorized, isMonitoring]);

  // Set up event listener for TikTok usage events
  useEffect(() => {
    if (!isAvailable || !isAuthorized) {
      return;
    }

    const unsubscribe = ScreenTimeModule.addTikTokUsageListener(handleTikTokUsageExceeded);

    return () => {
      unsubscribe();
    };
  }, [isAvailable, isAuthorized, handleTikTokUsageExceeded]);

  /**
   * Start TikTok usage monitoring
   */
  const startMonitoring = useCallback(async () => {
    if (!isAvailable) {
      console.warn('TikTok monitoring not available on this device');
      return { success: false, message: 'Not available on this device' };
    }

    if (!isAuthorized) {
      console.warn('Screen Time authorization required for TikTok monitoring');
      return { success: false, message: 'Screen Time authorization required' };
    }

    try {
      const result = await ScreenTimeModule.startTikTokMonitoring();
      if (result.success) {
        setIsMonitoring(true);
        console.log('TikTok monitoring started successfully');
      }
      return result;
    } catch (error) {
      console.error('Failed to start TikTok monitoring:', error);
      return { success: false, message: error.message };
    }
  }, [isAvailable, isAuthorized]);

  /**
   * Stop TikTok usage monitoring
   */
  const stopMonitoring = useCallback(async () => {
    if (!isMonitoring) {
      return { success: true, message: 'Monitoring not active' };
    }

    try {
      const result = await ScreenTimeModule.stopTikTokMonitoring();
      if (result.success) {
        setIsMonitoring(false);
        // Clear any pending TikTok notifications
        await NotificationManager.clearTikTokNotifications();
        console.log('TikTok monitoring stopped successfully');
      }
      return result;
    } catch (error) {
      console.error('Failed to stop TikTok monitoring:', error);
      return { success: false, message: error.message };
    }
  }, [isMonitoring]);

  /**
   * Request Screen Time authorization if needed
   */
  const requestAuthorization = useCallback(async () => {
    if (!isAvailable) {
      return { success: false, message: 'Not available on this device' };
    }

    try {
      const result = await ScreenTimeModule.requestAuthorization();
      if (result.status === 'authorized') {
        setIsAuthorized(true);
      }
      return result;
    } catch (error) {
      console.error('Failed to request Screen Time authorization:', error);
      return { success: false, message: error.message };
    }
  }, [isAvailable]);

  return {
    isMonitoring,
    isAvailable,
    isAuthorized,
    startMonitoring,
    stopMonitoring,
    requestAuthorization,
  };
};

export default useTikTokMonitoring;
