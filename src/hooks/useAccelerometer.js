import { useState, useEffect } from 'react';
import { Accelerometer } from 'expo-sensors';
import { isPhoneFaceDown, calculateScreamVolume, getAccelerometerInterval } from '../utils/accelerometerUtils';

/**
 * Custom hook for managing accelerometer data and phone state
 * @param {boolean} isActive - Whether accelerometer should be active
 * @returns {Object} Accelerometer state and handlers
 */
export const useAccelerometer = (isActive = true) => {
  const [accelerometerData, setAccelerometerData] = useState({ x: 0, y: 0, z: 0 });
  const [isFaceDown, setIsFaceDown] = useState(false);
  const [screamVolume, setScreamVolume] = useState(0.5);

  useEffect(() => {
    let subscription;

    if (isActive) {
      // Set initial update interval
      Accelerometer.setUpdateInterval(500);

      // Subscribe to accelerometer updates
      subscription = Accelerometer.addListener((accelerometerData) => {
        const { x, y, z } = accelerometerData;
        
        // Update raw data
        setAccelerometerData({ x, y, z });
        
        // Check if phone is face down
        const faceDown = isPhoneFaceDown(x, y, z);
        setIsFaceDown(faceDown);
        
        // Calculate volume if not face down
        if (!faceDown) {
          const volume = calculateScreamVolume(x, y, z);
          setScreamVolume(volume);
        }
        
        // Adjust update interval based on phone state
        const interval = getAccelerometerInterval(faceDown);
        Accelerometer.setUpdateInterval(interval);
      });
    }

    return () => {
      if (subscription) {
        subscription.remove();
      }
    };
  }, [isActive]);

  return {
    accelerometerData,
    isFaceDown,
    screamVolume,
    // Helper methods
    setUpdateInterval: Accelerometer.setUpdateInterval,
  };
};
