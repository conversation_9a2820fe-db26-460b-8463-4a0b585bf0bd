import React, { useState } from 'react';
import { TouchableOpacity, Alert, DeviceEventEmitter } from 'react-native';
import StyledText from './StyledText';
import ScreenTimeModule from '../ScreenTimeModule';

const ShieldTestButton = () => {

  const testShieldCommunication = async () => {
    try {
      // Set 10-minute timer for test
      const sessionDuration = 10 * 60; // 10 minutes in seconds
      const sessionStartTime = new Date();

      // Store timer info in shared UserDefaults
      await ScreenTimeModule.setFocusSessionTimer(sessionStartTime.getTime(), sessionDuration);

      // Start focus mode to enable shields
      const result = await ScreenTimeModule.startFocusMode();

      if (result.success) {
        // Notify main app to start timer display
        DeviceEventEmitter.emit('startTestTimer', {
          startTime: sessionStartTime.getTime(),
          duration: sessionDuration
        });

        Alert.alert(
          'Shield Test (10 min)',
          'Focus mode started with 10-minute countdown! Now try to open a blocked app to see the custom blocking screen with timer.\n\nThe blocking screen will show time remaining and sarcastic messages.',
          [
            {
              text: 'Stop Focus Mode',
              onPress: async () => {
                await ScreenTimeModule.stopFocusMode();
                await ScreenTimeModule.clearFocusSessionTimer();
                DeviceEventEmitter.emit('stopTestTimer');
              }
            },
            { text: 'OK' }
          ]
        );

        // Auto-stop after 10 minutes
        setTimeout(async () => {
          await ScreenTimeModule.stopFocusMode();
          await ScreenTimeModule.clearFocusSessionTimer();
          DeviceEventEmitter.emit('stopTestTimer');
        }, sessionDuration * 1000);

      } else {
        Alert.alert('Error', 'Failed to start focus mode: ' + result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to test shield: ' + error.message);
    }
  };

  return (
    <TouchableOpacity
      style={{
        marginTop: 10,
        paddingHorizontal: 15,
        paddingVertical: 8,
        backgroundColor: '#FF6B35',
        borderRadius: 8,
      }}
      onPress={testShieldCommunication}
    >
      <StyledText color="1" style={{ fontSize: 14, textAlign: 'center' }}>
        Test Shield Screens
      </StyledText>
    </TouchableOpacity>
  );
};

export default ShieldTestButton;
