/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import StyledText from './StyledText';
import Screaming from './Screaming';

// Test mode configuration - set to true to show test buttons and development features
const TEST_MODE = true;

/**
 * FocusScreen component - displayed when in focus mode
 */
const FocusScreen = ({ 
  focusSessionActive, 
  skipCountdown, 
  onEndSession,
  focuser = 'screamio' 
}) => {
  return (
    <View style={{ flex: 1 }}>
      <Screaming focuser={focuser} sessionActive={focusSessionActive} skipCountdown={skipCountdown} />
      
      {/* Emergency stop button - only shown in test mode */}
      {focusSessionActive && TEST_MODE && (
        <TouchableOpacity
          style={{
            position: 'absolute',
            bottom: 50,
            left: '50%',
            marginLeft: -75,
            width: 150,
            paddingVertical: 15,
            backgroundColor: 'rgba(255, 0, 0, 0.8)',
            borderRadius: 10,
            alignItems: 'center',
          }}
          onPress={onEndSession}
        >
          <StyledText color="1" style={{ fontSize: 16, fontWeight: 'bold' }}>
            End Session
          </StyledText>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default FocusScreen;