/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, Animated, Easing, DevSettings } from 'react-native';
import { Slider } from 'native-base';
import StyledText from './StyledText';
import Colors from '../constants/Colors';
import { useBrightnessPulse } from '../hooks/useBrightnessPulse';

const blurhash =
  '|E9@|kx[00IBs:j[%ztRaeMyRQtQx]bHRjV@V@oz00Rj_Mt7o|t7IBRQfkofx[j]IBVtj[tRx[RPt7ayj[oyRPRjt7afoy%fafMyaytQtQV@RQtQVtaeozoyV@WBoyoyWBozafRQoftRj[RjV[aeo}kBVtafoyafbHoyae';

// Test mode configuration - set to true to show test buttons and development features
const TEST_MODE = true;

const HomeScreen = ({
  sleepTime,
  setSleepTime,
  onStartFocusSession,
  onShowSetupModal,
  onShowScreemioStory,
  onQuitApp,
  screenTimeAvailable,
  screenTimeAuthorized,
  sessionTimeRemaining,
  notificationsScheduled,
  debugScreenTime,
  testBrightnessPulse,
  NotificationManager
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  // Start hovering animation for Screemio face
  useEffect(() => {
    startAnimation();
    return () => {
      stopAnimation();
    };
  }, []);

  // Start animation for hovering Screemio face
  const startAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 10,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 2000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ]),
      { iterations: -1 }
    ).start();
  };

  // Stop animation
  const stopAnimation = () => {
    Animated.timing(animatedValue).stop();
  };

  return (
    <View style={{ flex: 5 }}>
      <View style={styles.imageContainer}>
        <TouchableOpacity onPress={onShowScreemioStory}>
          <Animated.Image
            style={[
              styles.image,
              {
                transform: [
                  {
                    translateY: animatedValue,
                  },
                ],
              },
            ]}
            source={require('../../assets/images/screemio.png')}
            placeholder={blurhash}
            contentFit="contain"
            transition={1000}
          />
        </TouchableOpacity>
      </View>
      <View style={{ flex: 5, padding: 50 }}>
        <StyledText color="1" style={{ fontSize: 40, padding: 5, textAlign: 'center' }}>
          Let Screemio sleep for
        </StyledText>

        <StyledText color="1" style={{ fontSize: 40, padding: 5, textAlign: 'center' }}>
          {sleepTime} minutes
        </StyledText>

        {/* Session Timer */}
        {sessionTimeRemaining && (
          <View style={{ marginVertical: 10, alignItems: 'center' }}>
            <StyledText color="1" style={{ fontSize: 24, textAlign: 'center', fontWeight: 'bold' }}>
              {sessionTimeRemaining}
            </StyledText>
            {notificationsScheduled && (
              <StyledText color="3" style={{ fontSize: 14, textAlign: 'center', marginTop: 5 }}>
                Screaming notifications active
              </StyledText>
            )}
          </View>
        )}

        <View style={{ marginTop: 20, flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <Slider
            value={sleepTime}
            onChange={(value) => setSleepTime(value)}
            defaultValue={30}
            size="lg"
            step={5}
            minValue={15}
            maxValue={240}
          >
            <Slider.Track>
              <Slider.FilledTrack />
            </Slider.Track>
            <Slider.Thumb />
          </Slider>
        </View>
        
        {/* Conditional Start/Setup Button */}
        {screenTimeAvailable && screenTimeAuthorized ? (
          <TouchableOpacity
            style={{
              marginTop: 30,
            }}
            onPress={onStartFocusSession}
          >
            <StyledText color="1" style={{
              fontSize: 32,
              fontWeight: 'bold',
              textAlign: 'center',
              textShadowOffset: { width: 1, height: 1 },
              textShadowRadius: 2,
            }}>
              (start)
            </StyledText>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={{
              marginTop: 30,
              paddingVertical: 20,
              paddingHorizontal: 40,
              backgroundColor: 'rgba(224, 126, 160, 0.8)', // Semi-transparent color3
              borderRadius: 15,
              borderWidth: 3,
              borderColor: Colors.color3,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.3,
              shadowRadius: 6,
              elevation: 8,
            }}
            onPress={onShowSetupModal}
          >
            <StyledText color="3" style={{
              fontSize: 28,
              fontWeight: 'bold',
              textAlign: 'center',
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowOffset: { width: 1, height: 1 },
              textShadowRadius: 2,
            }}>
              SETUP REQUIRED
            </StyledText>
          </TouchableOpacity>
        )}

        <StyledText color="0" style={{ fontSize: 30, paddingTop: 50, textAlign: 'center' }}>
          or
        </StyledText>

        <TouchableOpacity
          style={{}}
          onPress={onQuitApp}
        >
          <StyledText color="3" style={{
            fontSize: 24,
            fontWeight: '600',
            textAlign: 'center',
            padding: 5,
          }}>
            Be a jerk and don't let Screemio sleep
          </StyledText>
        </TouchableOpacity>

        {/* Test Notification Buttons - only shown in test mode */}
        {TEST_MODE && (
          <View style={{ marginTop: 20, alignItems: 'center' }}>
            <TouchableOpacity
              style={{
                paddingVertical: 10,
                paddingHorizontal: 20,
                backgroundColor: 'rgba(255, 165, 0, 0.8)',
                borderRadius: 10,
                marginBottom: 10,
              }}
              onPress={async () => {
                const hasPermission = await NotificationManager.requestPermissions();
                if (hasPermission) {
                  await NotificationManager.scheduleTestNotification();
                  console.log('Test notification scheduled!');
                } else {
                  console.log('Notification permission denied');
                }
              }}
            >
              <StyledText color="1" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                Test Notification
              </StyledText>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                paddingVertical: 10,
                paddingHorizontal: 20,
                backgroundColor: 'rgba(255, 0, 0, 0.8)',
                borderRadius: 10,
                marginBottom: 10,
              }}
              onPress={async () => {
                const success = await NotificationManager.forceEraseAllNotifications();
                if (success) {
                  console.log('All notifications erased!');
                } else {
                  console.log('Failed to erase notifications');
                }
              }}
            >
              <StyledText color="1" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                Erase All Notifications
              </StyledText>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                paddingVertical: 10,
                paddingHorizontal: 20,
                backgroundColor: 'rgba(128, 0, 128, 0.8)',
                borderRadius: 10,
              }}
              onPress={() => {
                console.log('Testing optimized brightness pulse for 5 seconds...');
                testBrightnessPulse.startBrightnessPulse();
                setTimeout(() => {
                  testBrightnessPulse.stopBrightnessPulse();
                  console.log('Brightness pulse test completed');
                }, 5000);
              }}
            >
              <StyledText color="1" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                Test Brightness Pulse
              </StyledText>
            </TouchableOpacity>
          </View>
        )}

        {/* Development Buttons - only shown in test mode */}
        {__DEV__ && TEST_MODE && (
          <View style={{ position: 'absolute', top: 50, right: 20 }}>
            <TouchableOpacity
              style={{
                padding: 10,
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 5,
                marginBottom: 10
              }}
              onPress={() => DevSettings.reload()}
            >
              <StyledText color="1" style={{ fontSize: 12 }}>
                Reload
              </StyledText>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                padding: 10,
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: 5
              }}
              onPress={debugScreenTime}
            >
              <StyledText color="1" style={{ fontSize: 12 }}>
                Debug ST
              </StyledText>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    flex: 2,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: 200,
    height: 200,
  },
});

export default HomeScreen;