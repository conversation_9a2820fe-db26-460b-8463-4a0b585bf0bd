import React, { useState } from 'react';
import { TouchableOpacity, Alert } from 'react-native';
import StyledText from './StyledText';
import { ScreenTimeTest } from '../tests/ScreenTimeTest';

const ScreenTimeTestButton = () => {
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    if (isRunning) return;
    
    setIsRunning(true);
    
    try {
      console.log('🧪 Starting Screen Time tests...');
      await ScreenTimeTest.runAllTests();
      
      Alert.alert(
        'Screen Time Tests',
        'All tests completed successfully! Check console for detailed results.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Screen Time tests failed:', error);
      
      Alert.alert(
        'Screen Time Tests',
        `Tests failed: ${error.message}\n\nCheck console for details.`,
        [{ text: 'OK' }]
      );
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <TouchableOpacity
      style={{
        marginTop: 10,
        paddingHorizontal: 15,
        paddingVertical: 8,
        backgroundColor: isRunning ? '#666' : '#007AFF',
        borderRadius: 8,
        opacity: isRunning ? 0.6 : 1,
      }}
      onPress={runTests}
      disabled={isRunning}
    >
      <StyledText color="1" style={{ fontSize: 14, textAlign: 'center' }}>
        {isRunning ? 'Running Tests...' : 'Test Screen Time'}
      </StyledText>
    </TouchableOpacity>
  );
};

export default ScreenTimeTestButton;
