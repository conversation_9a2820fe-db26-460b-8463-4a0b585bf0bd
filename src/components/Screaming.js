/* eslint-disable react-native/no-inline-styles */
import {  useEffect, useState, useRef } from 'react';
import { View, Animated, StyleSheet, Easing, AppState } from 'react-native';
import { ScreamAudio } from '../screamEngine';
import StyledText from './StyledText';
import { useAccelerometer } from '../hooks/useAccelerometer';
import { useBrightnessPulse } from '../hooks/useBrightnessPulse';
//a functional react component that returns a animated face created using only react native components
const blurhash =
  '|E9@|kx[00IBs:j[%ztRaeMyRQtQx]bHRjV@V@oz00Rj_Mt7o|t7IBRQfkofx[j]IBVtj[tRx[RPt7ayj[oyRPRjt7afoy%fafMyaytQtQV@RQtQVtaeozoyV@WBoyoyWBozafRQoftRj[RjV[aeo}kBVtafoyafbHoyae';

const Face = (props) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const slowAnimatedValue = useRef(new Animated.Value(0)).current;
  const [scream] = useState(() => new ScreamAudio(props.focuser));
  const [countDown, setCountDown] = useState(0);
  const [screamOn, setScreamOn] = useState(false);
  const [isScreaming, setIsScreaming] = useState(false);
  const [appState, setAppState] = useState(AppState.currentState);

  // Use custom hooks for accelerometer and brightness
  const { isFaceDown, screamVolume } = useAccelerometer(screamOn);
  const { startBrightnessPulse, stopBrightnessPulse, cleanup: cleanupBrightness } = useBrightnessPulse();

  const initializeScreamPowers = () => {
    setScreamOn(true);
    // Accelerometer is now handled by the useAccelerometer hook
    // Brightness pulsing will start when screaming begins
  };

  useEffect(() => {
    // Skip countdown if specified in props (e.g., when returning from notification)
    if (props.skipCountdown) {
      console.log('Skipping countdown - going directly to focus mode');
      setCountDown(0);
      initializeScreamPowers();
    } else {
      recursiveCountDown(6);
    }

    // Add app state change listener
    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      scream.cleanup(); // Use cleanup method instead of just stopScream
      stopAnimation();
      cleanupBrightness(); // Stop brightness pulsing
      appStateSubscription?.remove();
    };
  }, [props.skipCountdown]);

  const handleAppStateChange = (nextAppState) => {
    console.log('Screaming component - App state changed from', appState, 'to', nextAppState);

    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      console.log('App came to foreground - accelerometer handled by hook');
    } else if (appState === 'active' && nextAppState.match(/inactive|background/)) {
      // App has gone to the background
      console.log('App went to background - screaming will continue if active');
      // Accelerometer continues in background via hook
    }

    setAppState(nextAppState);
  };

  const recursiveCountDown = (count) => {
    if (count > 0) {
      setCountDown(count - 1);
      setTimeout(() => {
        recursiveCountDown(count - 1);
      }, 1000);
    } else {
      initializeScreamPowers();
      // Accelerometer is now handled by the useAccelerometer hook
    }
  };

  useEffect(() => {
    if (isScreaming) {
      startAnimation();
    } else {
      stopAnimation();
    
    }
  }, [isScreaming]);

  const startAnimation = () => {
    console.log('start animation');
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 10,
          duration: 100,
          easing: Easing.bezier(0, 10, 1, -3),
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 100,
          easing: Easing.bezier(0, 10, 1, -4),
          useNativeDriver: true,
        }),
      ]),
      { iterations: -1 }
    ).start();
    Animated.loop(
      Animated.sequence([
        Animated.timing(slowAnimatedValue, {
          toValue: 10,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
        Animated.timing(slowAnimatedValue, {
          toValue: 0,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        }),
      ]),
      { iterations: -1 }
    ).start();
  };

  // lets make a functions that stops the animation loop
  const stopAnimation = () => {
    animatedValue.stopAnimation();
    slowAnimatedValue.stopAnimation();
  };

  // Handle phone state changes from accelerometer hook
  useEffect(() => {
    if (countDown === 0 && screamOn) {
      if (isFaceDown) {
        // Phone is face down (stop screaming)
        if (scream && isScreaming) {
          scream.stopScream();
          stopAnimation();
          // Stop brightness pulsing when screaming stops
          stopBrightnessPulse();
          setIsScreaming(false);
        }
      } else {
        // Phone is not face down (start/continue screaming)
        if (scream) {
          scream.startScream(screamVolume);
          // Start brightness pulsing if not already screaming
          if (!isScreaming) {
            startBrightnessPulse();
          }
          setIsScreaming(true);
        }
      }
    }
  }, [isFaceDown, screamVolume, countDown, screamOn, isScreaming]);

  // Stop brightness pulsing when session ends
  useEffect(() => {
    if (props.sessionActive === false && isScreaming) {
      console.log('Session ended - stopping brightness pulsing');
      stopBrightnessPulse();
      setIsScreaming(false);
    }
  }, [props.sessionActive, isScreaming, stopBrightnessPulse]);

  return (
    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
      {countDown ? (
        <StyledText style={{ fontSize: 150 }} color="2">
          {countDown}
        </StyledText>
      ) : (
        <>
          <View style={styles.imageContainer}>
            {isScreaming && (
              <Animated.Image
                style={[
                  styles.image,
                  {
                    transform: [
                      {
                        translateY: animatedValue.interpolate({
                          inputRange: [0, 10],
                          outputRange: [-1, 1],
                        }),
                      },
                      {
                        translateX: animatedValue.interpolate({
                          inputRange: [0, 10],
                          outputRange: [0.5, 1],
                        }),
                      },
                      {
                        scale: slowAnimatedValue.interpolate({
                          inputRange: [0, 10],
                          outputRange: [0, 5],
                        }),
                      },
                    ],
                  },
                ]}
                source={require('../../assets/images/screemio.png')}
                placeholder={blurhash}
                contentFit="contain"
                transition={1000}
              />
            )}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    flex: 2,
    height: 100,
    // backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    // flex: 2,
    width: 200,
    height: 200,
    // backgroundColor: '#0553',
  },
});

export default Face;
