/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Text } from 'react-native';
import Colors from '../constants/Colors';
import { useFonts } from 'expo-font';
const StyledText = (props) => {
  const activeColor = props.color ? 'color' + props.color : 'color1';
  const color = Colors[activeColor];
  const [fontsLoaded] = useFonts({
    CarrolWild: require('../../assets/fonts/stamp.ttf'),
  });
  if (fontsLoaded) {
    return <Text {...props} style={[{ fontFamily: 'CarrolWild', fontWeight: '800', color: color }, props.style]} />;
  }
  return null;
};

export default StyledText;
