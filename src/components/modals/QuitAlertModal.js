/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Modal, TouchableOpacity } from 'react-native';
import StyledText from '../StyledText';
import Colors from '../../constants/Colors';

/**
 * QuitAlertModal - shown when the user tries to quit
 */
const QuitAlertModal = ({ visible, onClose, onConfirmQuit }) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <View style={{
          backgroundColor: Colors.color2,
          borderRadius: 20,
          padding: 20,
          paddingTop: 30,
          width: '90%',
          maxWidth: 350,
          borderWidth: 3,
          borderColor: Colors.color3,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.5,
          shadowRadius: 20,
          elevation: 20,
        }}>
          {/* Title */}
          <StyledText color="3" style={{
            fontSize: 28,
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: 20,
          }}>
            Fine, be that way!
          </StyledText>

          {/* Message */}
          <StyledText color="1" style={{
            fontSize: 18,
            textAlign: 'center',
            marginBottom: 30,
            lineHeight: 24,
          }}>
            Since you're being a jerk, just swipe up from the bottom and flick the app away to close it. Screemio will remember this betrayal...
          </StyledText>

          {/* Buttons */}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 15 }}>
            <TouchableOpacity
              style={{
                flex: 1,
              }}
              onPress={onClose}
            >
              <StyledText color="1" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                I changed my mind!
              </StyledText>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flex: 1,
              }}
              onPress={onConfirmQuit}
            >
              <StyledText color="3" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                I'm still being a jerk
              </StyledText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default QuitAlertModal;