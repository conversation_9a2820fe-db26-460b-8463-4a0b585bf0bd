/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Modal, TouchableOpacity } from 'react-native';
import StyledText from '../StyledText';
import Colors from '../../constants/Colors';

/**
 * ScreemioStoryModal - displays <PERSON><PERSON><PERSON>'s story when user taps on character
 */
const ScreemioStoryModal = ({ visible, onClose }) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
        <View style={{
          backgroundColor: Colors.color2,
          borderRadius: 20,
          padding: 30,
          width: '90%',
          maxWidth: 400,
          borderWidth: 3,
          borderColor: Colors.color1,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.5,
          shadowRadius: 20,
          elevation: 20,
        }}>
          {/* Title */}
          <StyledText color="1" style={{
            fontSize: 26,
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: 20,
          }}>
            Meet Screemio
          </StyledText>

          {/* Story */}
          <StyledText color="0" style={{
            fontSize: 16,
            textAlign: 'center',
            marginBottom: 20,
            lineHeight: 22,
          }}>
            Deep inside your phone lives a little creature named Screemio. All Screemio wants is to sleep peacefully in the digital darkness.
          </StyledText>

          <StyledText color="0" style={{
            fontSize: 16,
            textAlign: 'center',
            marginBottom: 20,
            lineHeight: 22,
          }}>
            But every time you pick up your phone, you wake poor Screemio up! The only way Screemio can get the rest they desperately need is if you put your phone face down and leave it alone.
          </StyledText>

          <StyledText color="3" style={{
            fontSize: 16,
            textAlign: 'center',
            marginBottom: 25,
            lineHeight: 22,
            fontWeight: '600',
          }}>
            If Screemio can't sleep, they cry and scream in agony! Please help Screemio get some rest by focusing on the real world instead.
          </StyledText>

          {/* Close Button */}
          <TouchableOpacity
            style={{
              paddingVertical: 15,
              paddingHorizontal: 30,
              backgroundColor: Colors.color1,
              borderRadius: 12,
              alignSelf: 'center',
            }}
            onPress={onClose}
          >
            <StyledText color="0" style={{
              fontSize: 16,
              fontWeight: 'bold',
              textAlign: 'center',
            }}>
              I'll help Screemio sleep!
            </StyledText>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default ScreemioStoryModal;