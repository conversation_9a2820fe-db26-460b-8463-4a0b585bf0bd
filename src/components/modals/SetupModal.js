/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Modal, TouchableOpacity } from 'react-native';
import StyledText from '../StyledText';
import Colors from '../../constants/Colors';

/**
 * SetupModal - displays Screen Time setup information
 */
const SetupModal = ({ visible, onClose, onEnableScreenTime }) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
        <View style={{
          backgroundColor: Colors.color2,
          borderRadius: 20,
          padding: 30,
          width: '90%',
          maxWidth: 400,
          borderWidth: 3,
          borderColor: Colors.color3,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 10 },
          shadowOpacity: 0.5,
          shadowRadius: 20,
          elevation: 20,
        }}>
          {/* Title */}
          <StyledText color="3" style={{
            fontSize: 26,
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: 20,
          }}>
            Setup Required
          </StyledText>

          {/* Message */}
          <StyledText color="1" style={{
            fontSize: 16,
            textAlign: 'center',
            marginBottom: 20,
            lineHeight: 22,
          }}>
            Screemio needs you to set up Screen Time permissions to block other apps during focus sessions.
          </StyledText>

          <StyledText color="0" style={{
            fontSize: 16,
            textAlign: 'center',
            marginBottom: 20,
            lineHeight: 22,
          }}>
            This feature will temporarily block access to other apps while you focus. Please use it considerately and responsibly.
          </StyledText>

          <StyledText color="3" style={{
            fontSize: 14,
            textAlign: 'center',
            marginBottom: 25,
            lineHeight: 20,
            fontStyle: 'italic',
          }}>
            Note: You can always disable this in your device's Screen Time settings if needed.
          </StyledText>

          {/* Buttons */}
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 15 }}>
            <TouchableOpacity
              style={{
                flex: 1,
                paddingVertical: 15,
              }}
              onPress={onClose}
            >
              <StyledText color="1" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                Maybe Later
              </StyledText>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flex: 1,
                paddingVertical: 15,
                paddingHorizontal: 20,
                backgroundColor: Colors.color3,
                borderRadius: 12,
              }}
              onPress={onEnableScreenTime}
            >
              <StyledText color="0" style={{
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
                Enable Screen Time
              </StyledText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default SetupModal;