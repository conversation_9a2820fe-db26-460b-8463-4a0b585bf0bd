import * as Notifications from 'expo-notifications';
import { Platform, AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

class NotificationManager {
  constructor() {
    this.scheduledNotifications = [];
    this.focusSessionData = null;
    this.screamSounds = [
      '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'
    ];
    this.appStateSubscription = null;
    this.isInFocusMode = false;
  }

  /**
   * Request notification permissions
   */
  async requestPermissions() {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.warn('Notification permission not granted');
        return false;
      }
      
      console.log('Notification permissions granted');
      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  /**
   * Schedule 100 notifications every 10 seconds
   */
  async scheduleScreamingNotifications(sessionStartTime, sessionDuration) {
    console.log('Scheduling screaming notifications...');
    try {
      // Clear any existing notifications first
      await this.clearAllNotifications();

      // Store session data for restoration
      this.focusSessionData = { sessionStartTime, sessionDuration };
      await AsyncStorage.setItem('focusSessionData', JSON.stringify(this.focusSessionData));

      const notifications = [];
      const currentTime = Date.now();
      const sessionEndTime = sessionStartTime + (sessionDuration * 1000);

      // Start scheduling from current time (when app went to background)
      let nextNotificationTime = currentTime + 10000; // First notification in 10 seconds
      console.log('Scheduling notifications starting from', nextNotificationTime);

      // Schedule up to 100 notifications, each 10 seconds apart
      for (let i = 0; i < 100; i++) {
        // Don't schedule notifications beyond session end time
        if (nextNotificationTime > sessionEndTime) {
          break;
        }
        console.log('Scheduling notification for', nextNotificationTime);

        // Calculate remaining time for this notification
        const remainingSeconds = Math.max(0, Math.floor((sessionEndTime - nextNotificationTime) / 1000));
        const remainingMinutes = Math.floor(remainingSeconds / 60);
        const remainingSecondsDisplay = remainingSeconds % 60;
        const timeDisplay = `${remainingMinutes}:${remainingSecondsDisplay.toString().padStart(2, '0')}`;

        // Select random scream sound
        const screamSound = this.screamSounds[i % this.screamSounds.length];

        const notificationContent = {
          title: 'SCREEMIO IS SCREAMING!',
          body: `Focus session continues! ${timeDisplay} remaining. Tap to return to focus mode!`,
          sound: Platform.OS === 'ios' ? `${screamSound}.mp3` : `${screamSound}`,
          data: {
            type: 'focus_session',
            sessionStartTime,
            sessionDuration,
            screamSound,
          },
        };

        const secondsFromNow = Math.floor((nextNotificationTime - currentTime) / 1000);

        try {
          const notificationId = await Notifications.scheduleNotificationAsync({
            content: notificationContent,
            trigger: { seconds: secondsFromNow },
          });

          notifications.push(notificationId);
          console.log(`Scheduled notification ${i + 1} for ${secondsFromNow}s from now (${timeDisplay} remaining)`);
        } catch (error) {
          console.error(`Failed to schedule notification ${i + 1}:`, error);
        }

        // Next notification in 10 seconds
        nextNotificationTime += 10000;
      }

      this.scheduledNotifications = notifications;
      console.log(`Successfully scheduled ${notifications.length} screaming notifications`);

      return notifications.length;
    } catch (error) {
      console.error('Error scheduling screaming notifications:', error);
      return 0;
    }
  }

  /**
   * Clear all scheduled notifications
   */
  async clearAllNotifications() {
    try {
      // Cancel all scheduled notifications
      await Notifications.cancelAllScheduledNotificationsAsync();

      // Dismiss all delivered notifications
      await Notifications.dismissAllNotificationsAsync();

      // Clear internal state
      this.scheduledNotifications = [];
      this.focusSessionData = null;
      await AsyncStorage.removeItem('focusSessionData');

      console.log('All notifications cleared');
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  /**
   * Get stored focus session data
   */
  async getStoredFocusSession() {
    try {
      const data = await AsyncStorage.getItem('focusSessionData');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting stored focus session:', error);
      return null;
    }
  }

  /**
   * Handle notification response (when user taps notification)
   */
  handleNotificationResponse(response) {
    const data = response.notification.request.content.data;
    
    if (data.type === 'focus_session') {
      console.log('User tapped focus session notification');
      return {
        shouldOpenFocusMode: true,
        sessionStartTime: data.sessionStartTime,
        sessionDuration: data.sessionDuration,
        screamSound: data.screamSound,
      };
    }
    
    return null;
  }

  /**
   * Check if focus session is still active
   */
  isFocusSessionActive(sessionStartTime, sessionDuration) {
    if (!sessionStartTime || !sessionDuration) return false;

    const currentTime = Date.now();
    const sessionEndTime = sessionStartTime + (sessionDuration * 1000);

    return currentTime < sessionEndTime;
  }

  /**
   * Start monitoring focus mode - schedule notifications when app goes to background
   */
  async startFocusModeMonitoring(sessionStartTime, sessionDuration) {
    this.isInFocusMode = true;
    this.focusSessionData = { sessionStartTime, sessionDuration };

    // Listen for app state changes
    this.appStateSubscription = AppState.addEventListener('change', async (nextAppState) => {
      if (this.isInFocusMode && nextAppState === 'background') {
        console.log('App went to background during focus mode - scheduling notifications');
        await this.scheduleScreamingNotifications(sessionStartTime, sessionDuration);
        console.log('Notifications scheduled:', this.scheduledNotifications.length);
        
      } else if (nextAppState === 'active') {
        console.log('App became active - clearing notifications');
        this.clearAllNotifications();
      }
    });
  }

  /**
   * Stop monitoring focus mode
   */
  stopFocusModeMonitoring() {
    this.isInFocusMode = false;
    this.focusSessionData = null;

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    console.log('Focus mode monitoring stopped');
    this.clearAllNotifications();
  }

  /**
   * Test function to schedule a single notification immediately
   */
  async scheduleTestNotification() {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'TEST SCREEMIO NOTIFICATION!',
          body: 'This is a test notification to verify notifications are working!',
          sound: Platform.OS === 'ios' ? '1.mp3' : '1',
          data: {
            type: 'test',
          },
        },
        trigger: { seconds: 2 },
      });

      console.log('Test notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling test notification:', error);
      return null;
    }
  }

  /**
   * Force clear all notifications (including system notifications)
   */
  async forceEraseAllNotifications() {
    try {
      // Cancel all scheduled notifications
      await Notifications.cancelAllScheduledNotificationsAsync();

      // Dismiss all delivered notifications
      await Notifications.dismissAllNotificationsAsync();

      // Clear internal state
      this.scheduledNotifications = [];
      this.focusSessionData = null;
      await AsyncStorage.removeItem('focusSessionData');

      console.log('All notifications erased (scheduled and delivered)');
      return true;
    } catch (error) {
      console.error('Error erasing all notifications:', error);
      return false;
    }
  }

  /**
   * Send immediate screaming notification for TikTok usage
   */
  async sendTikTokUsageNotification() {
    try {
      // Select random scream sound
      const screamSound = this.screamSounds[Math.floor(Math.random() * this.screamSounds.length)];

      const notificationContent = {
        title: 'SCREEMIO IS SCREAMING!',
        body: 'You\'ve been on TikTok for over 1 minute! Time to focus! 😱',
        sound: Platform.OS === 'ios' ? `${screamSound}.mp3` : `${screamSound}`,
        data: {
          type: 'tiktok_usage_exceeded',
          timestamp: Date.now(),
          screamSound,
        },
      };

      await Notifications.scheduleNotificationAsync({
        content: notificationContent,
        trigger: null, // Send immediately
      });

      console.log('TikTok usage notification sent');
      return true;
    } catch (error) {
      console.error('Error sending TikTok usage notification:', error);
      return false;
    }
  }

  /**
   * Schedule repeating screaming notifications for TikTok usage
   */
  async scheduleRepeatingTikTokNotifications() {
    try {
      // Clear any existing TikTok notifications
      await this.clearTikTokNotifications();

      // Schedule notifications every 30 seconds for the next 10 minutes
      const notifications = [];
      for (let i = 1; i <= 20; i++) { // 20 notifications over 10 minutes
        const screamSound = this.screamSounds[(i - 1) % this.screamSounds.length];

        const notificationContent = {
          title: 'SCREEMIO IS STILL SCREAMING!',
          body: `Stop scrolling TikTok! Focus time! Notification ${i}/20 😱`,
          sound: Platform.OS === 'ios' ? `${screamSound}.mp3` : `${screamSound}`,
          data: {
            type: 'tiktok_usage_repeated',
            timestamp: Date.now(),
            screamSound,
            notificationNumber: i,
          },
        };

        const notification = await Notifications.scheduleNotificationAsync({
          content: notificationContent,
          trigger: {
            seconds: i * 30, // Every 30 seconds
          },
        });

        notifications.push(notification);
      }

      console.log(`Scheduled ${notifications.length} repeating TikTok notifications`);
      return notifications;
    } catch (error) {
      console.error('Error scheduling repeating TikTok notifications:', error);
      return [];
    }
  }

  /**
   * Clear TikTok-specific notifications
   */
  async clearTikTokNotifications() {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const tiktokNotifications = scheduledNotifications.filter(
        notification =>
          notification.content.data?.type === 'tiktok_usage_exceeded' ||
          notification.content.data?.type === 'tiktok_usage_repeated'
      );

      for (const notification of tiktokNotifications) {
        await Notifications.cancelScheduledNotificationAsync(notification.identifier);
      }

      console.log(`Cleared ${tiktokNotifications.length} TikTok notifications`);
      return true;
    } catch (error) {
      console.error('Error clearing TikTok notifications:', error);
      return false;
    }
  }
}

export default new NotificationManager();
