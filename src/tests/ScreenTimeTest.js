/**
 * Screen Time Module Test
 * 
 * This file contains test functions to verify Screen Time functionality
 * Run these tests to ensure the Screen Time integration is working properly
 */

import ScreenTimeModule from '../ScreenTimeModule';

export class ScreenTimeTest {
  
  static async runAllTests() {
    console.log('🧪 Starting Screen Time Tests...');
    
    try {
      await this.testAvailability();
      await this.testAuthorization();
      await this.testFocusMode();
      console.log('✅ All Screen Time tests completed successfully!');
    } catch (error) {
      console.error('❌ Screen Time tests failed:', error);
    }
  }

  static async testAvailability() {
    console.log('📱 Testing Screen Time availability...');
    
    const isAvailable = ScreenTimeModule.isAvailable();
    console.log(`Screen Time available: ${isAvailable}`);
    
    if (!isAvailable) {
      console.warn('⚠️ Screen Time is not available on this device/platform');
      return;
    }
    
    console.log('✅ Screen Time availability test passed');
  }

  static async testAuthorization() {
    console.log('🔐 Testing Screen Time authorization...');
    
    if (!ScreenTimeModule.isAvailable()) {
      console.log('⏭️ Skipping authorization test - Screen Time not available');
      return;
    }

    try {
      // Check current status
      const currentStatus = await ScreenTimeModule.getAuthorizationStatus();
      console.log('Current authorization status:', currentStatus);

      // If not authorized, request authorization
      if (currentStatus.status !== 'approved') {
        console.log('📝 Requesting Screen Time authorization...');
        const authResult = await ScreenTimeModule.requestAuthorization();
        console.log('Authorization result:', authResult);
      }

      // Verify final status
      const finalStatus = await ScreenTimeModule.getAuthorizationStatus();
      console.log('Final authorization status:', finalStatus);
      
      if (finalStatus.status === 'approved') {
        console.log('✅ Screen Time authorization test passed');
      } else {
        console.log('⚠️ Screen Time authorization not granted');
      }
    } catch (error) {
      console.error('❌ Authorization test failed:', error);
      throw error;
    }
  }

  static async testFocusMode() {
    console.log('🎯 Testing Focus Mode functionality...');
    
    if (!ScreenTimeModule.isAvailable()) {
      console.log('⏭️ Skipping focus mode test - Screen Time not available');
      return;
    }

    try {
      // Check authorization first
      const authStatus = await ScreenTimeModule.getAuthorizationStatus();
      if (authStatus.status !== 'approved') {
        console.log('⚠️ Skipping focus mode test - not authorized');
        return;
      }

      // Test starting focus mode
      console.log('🚀 Starting focus mode...');
      const startResult = await ScreenTimeModule.startFocusMode();
      console.log('Start focus mode result:', startResult);

      // Check if focus mode is active
      await this.delay(1000); // Wait a moment
      const activeStatus = await ScreenTimeModule.isFocusModeActive();
      console.log('Focus mode active status:', activeStatus);

      // Test stopping focus mode
      console.log('🛑 Stopping focus mode...');
      const stopResult = await ScreenTimeModule.stopFocusMode();
      console.log('Stop focus mode result:', stopResult);

      // Verify focus mode is stopped
      await this.delay(1000); // Wait a moment
      const finalStatus = await ScreenTimeModule.isFocusModeActive();
      console.log('Final focus mode status:', finalStatus);

      console.log('✅ Focus mode test completed');
    } catch (error) {
      console.error('❌ Focus mode test failed:', error);
      throw error;
    }
  }

  static async testEventListeners() {
    console.log('📡 Testing Screen Time event listeners...');
    
    if (!ScreenTimeModule.isAvailable()) {
      console.log('⏭️ Skipping event listener test - Screen Time not available');
      return;
    }

    try {
      // Add event listeners
      const authListener = ScreenTimeModule.addEventListener('onAuthorizationChanged', (event) => {
        console.log('📢 Authorization changed event:', event);
      });

      const errorListener = ScreenTimeModule.addEventListener('onScreenTimeError', (event) => {
        console.log('📢 Screen Time error event:', event);
      });

      console.log('✅ Event listeners added successfully');

      // Clean up after a short delay
      setTimeout(() => {
        ScreenTimeModule.removeEventListener('onAuthorizationChanged');
        ScreenTimeModule.removeEventListener('onScreenTimeError');
        console.log('🧹 Event listeners cleaned up');
      }, 5000);

    } catch (error) {
      console.error('❌ Event listener test failed:', error);
      throw error;
    }
  }

  static async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Integration test with App focus session
  static async testFocusSessionIntegration() {
    console.log('🔗 Testing Screen Time integration with focus sessions...');

    try {
      // Test Screen Time methods directly
      const status = await ScreenTimeModule.getAuthorizationStatus();
      console.log('Screen Time authorization status:', status);

      if (status.status === 'approved') {
        console.log('🎯 Testing focus session lifecycle...');

        // Simulate starting a focus session
        console.log('Starting focus session...');
        await ScreenTimeModule.startFocusMode();
        await this.delay(2000);

        // Check if focus mode is active
        const activeStatus = await ScreenTimeModule.isFocusModeActive();
        console.log('Focus mode active during session:', activeStatus);

        // Simulate ending the focus session
        console.log('Ending focus session...');
        await ScreenTimeModule.stopFocusMode();

        console.log('✅ Focus session integration test passed');
      } else {
        console.log('⚠️ Focus session integration test skipped - not authorized');
      }
    } catch (error) {
      console.error('❌ Focus session integration test failed:', error);
      throw error;
    }
  }
}

// Export individual test functions for manual testing
export const {
  testAvailability,
  testAuthorization,
  testFocusMode,
  testEventListeners,
  testFocusSessionIntegration,
  runAllTests
} = ScreenTimeTest;
