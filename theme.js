import { extendTheme } from 'native-base';

export const theme = extendTheme({
  fontConfig: {
    CarrolWild: {
      100: { normal: 'CarrolWild' },
      200: { normal: 'CarrolWild' },
      300: { normal: 'CarrolWild' },
      400: { normal: 'CarrolWild' },
      500: { normal: 'CarrolWild' },
      600: { normal: 'CarrolWild' },
      700: { normal: 'CarrolWild' },
      800: { normal: 'CarrolWild' },
      900: { normal: 'CarrolWild' },
    },
  },

  // Make sure values below matches any of the keys in `fontConfig`
  fonts: {
    heading: 'CarrolWild',
    body: 'CarrolWild',
    mono: 'CarrolWild',
  },
});
